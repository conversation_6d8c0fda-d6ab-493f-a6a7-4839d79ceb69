<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Inclure les fichiers nécessaires
require_once BASE_PATH . "/config/db.php";
require_once BASE_PATH . "/model/userModel.php";

// Fonction pour générer un token de test
function generateTestToken($username) {
    try {
        // Générer un token unique
        $token = bin2hex(random_bytes(32));

        // Définir la date d'expiration (24 heures)
        $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Stocker le token dans la base de données
        $result = storePasswordResetToken($username, $token, $expiry);

        if (isset($result['error'])) {
            return ['success' => false, 'error' => $result['error']];
        }

        // Construire le lien d'initialisation
        $host = $_SERVER['HTTP_HOST'];

        // Récupérer le chemin de base dynamiquement
        require_once BASE_PATH . "/config/constants.php";
        $link = "http://$host" . BASE_URL . "/view/initialize-password.php?token=$token";

        return [
            'success' => true,
            'token' => $token,
            'link' => $link,
            'expiry' => $expiry,
            'username' => $username
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Vérifier si un nom d'utilisateur est fourni
$username = isset($_GET['username']) ? $_GET['username'] : 'test_user';

// Vérifier si l'utilisateur existe
$userExists = getUserByUsername($username);

// Si l'utilisateur n'existe pas, le créer
if (isset($userExists['error'])) {
    echo "<p>L'utilisateur $username n'existe pas. Création d'un utilisateur de test...</p>";

    // Créer un utilisateur de test
    $tempPassword = password_hash('password', PASSWORD_DEFAULT);
    $createResult = createUser($username, $tempPassword, 'etudiant');

    if (isset($createResult['error'])) {
        die("Erreur lors de la création de l'utilisateur: " . $createResult['error']);
    }

    echo "<p>Utilisateur de test créé avec succès.</p>";
} else {
    echo "<p>L'utilisateur $username existe déjà.</p>";
}

// Générer un token pour l'utilisateur
$result = generateTestToken($username);

if ($result['success']) {
    echo "<h2>Token généré avec succès</h2>";
    echo "<p><strong>Nom d'utilisateur:</strong> " . htmlspecialchars($result['username']) . "</p>";
    echo "<p><strong>Token:</strong> " . htmlspecialchars($result['token']) . "</p>";
    echo "<p><strong>Date d'expiration:</strong> " . htmlspecialchars($result['expiry']) . "</p>";
    echo "<p><strong>Lien d'initialisation:</strong> <a href='" . htmlspecialchars($result['link']) . "' target='_blank'>" . htmlspecialchars($result['link']) . "</a></p>";

    // Ajouter un bouton pour ouvrir le lien dans un nouvel onglet
    echo "<button onclick=\"window.open('" . htmlspecialchars($result['link']) . "', '_blank')\">Ouvrir la page d'initialisation</button>";
} else {
    echo "<h2>Erreur lors de la génération du token</h2>";
    echo "<p>" . htmlspecialchars($result['error']) . "</p>";
}
?>
