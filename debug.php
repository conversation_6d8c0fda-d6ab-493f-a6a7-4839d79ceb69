<?php
// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure les fichiers nécessaires
require_once "model/userModel.php";
require_once "model/etudiantModel.php";
require_once "utils/emailSender.php";

// Tester la fonction createUser
echo "<h2>Test de createUser</h2>";
try {
    $result = createUser('test_user', password_hash('password', PASSWORD_DEFAULT), 'etudiant');
    echo "Résultat: ";
    print_r($result);
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}

// Tester la fonction sendEmail
echo "<h2>Test de sendEmail</h2>";
try {
    $result = sendEmail('<EMAIL>', 'Test Subject', 'Test Message');
    echo "Résultat: ";
    print_r($result);
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}

// Vérifier la configuration de la base de données
echo "<h2>Test de connexion à la base de données</h2>";
try {
    require_once "config/db.php";
    $conn = getConnection();
    if ($conn) {
        echo "Connexion à la base de données réussie";
        
        // Vérifier si la table users existe
        $result = mysqli_query($conn, "SHOW TABLES LIKE 'users'");
        if (mysqli_num_rows($result) > 0) {
            echo "<br>La table 'users' existe";
        } else {
            echo "<br>La table 'users' n'existe pas";
        }
        
        mysqli_close($conn);
    } else {
        echo "Échec de la connexion à la base de données";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
