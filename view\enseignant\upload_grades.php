<?php
// Include the authentication check for teachers
require_once '../includes/auth_check_enseignant.php';
// Récupérer les informations de l'enseignant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Enseignant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Grades - Teacher Dashboard</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <!-- CSS personnalisé pour la page import_grades - appliqué également à upload_grades -->
    <link rel="stylesheet" href="../assets/css/import_grades_custom.css">

    <!-- Styles spécifiques pour adapter le style des filtres -->
    <style>
        /* Styles supplémentaires pour les filtres */
        .filter-card {
            background: var(--light-gradient);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: none;
            animation: slideIn 0.5s ease-in-out;
        }

        .filter-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #4a4a4a;
            position: relative;
            padding-bottom: 10px;
        }

        .filter-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 3px;
        }

        /* Animation pour la transition en douceur */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include_once '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <?php include_once '../includes/header.php'; ?>

            <!-- Page Content -->
            <div class="container-fluid p-4">
                <h1 class="page-title">Upload Grades</h1>
                <p class="text-muted">Saisissez et gérez les notes des étudiants pour vos modules.</p>

                <!-- Filtres avec le style de import_grades.php mais la structure originale -->
                <div class="filter-card">
                    <h2 class="filter-title">Filtres</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filiere" class="form-label">Field</label>
                                <select id="filiere" class="form-select">
                                    <option value="" disabled selected>Select field</option>
                                    <!-- Options will be loaded dynamically -->
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="niveau" class="form-label">Level</label>
                                <select id="niveau" class="form-select">
                                    <option value="" disabled selected>Select level</option>
                                    <!-- Options will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="semestre" class="form-label">Semester</label>
                                <select id="semestre" class="form-select">
                                    <option value="" disabled selected>Select semester</option>
                                    <option value="S1">S1</option>
                                    <option value="S2">S2</option>
                                    <option value="S3">S3</option>
                                    <option value="S4">S4</option>
                                    <option value="S5">S5</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="session" class="form-label">Session</label>
                                <select id="session" class="form-select">
                                    <option value="" disabled selected>Select session</option>
                                    <option value="normale">Normal</option>
                                    <option value="rattrapage">Retake</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="module" class="form-label">Module</label>
                                <select id="module" class="form-select">
                                    <option value="" disabled selected>Select module</option>
                                    <!-- Options will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <button id="display-button" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Display Students
                            </button>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <!-- Initialize teacher ID for JavaScript -->
    <script>
        // Pass the teacher ID from PHP to JavaScript
        var currentTeacherId = <?php echo json_encode($teacherId); ?>;
        console.log('Teacher ID from PHP:', currentTeacherId);
    </script>

    <script src="../assets/js/upload_grades.js"></script>
</body>
</html>