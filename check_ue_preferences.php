<?php
// Script pour vérifier la structure de la table ue_preferences

// Inclure la configuration de la base de données
require_once 'config/db.php';

// Obtenir une connexion à la base de données
$conn = getConnection();

if (!$conn) {
    die("Erreur de connexion à la base de données");
}

// Vérifier si la table existe
$tableExists = $conn->query("SHOW TABLES LIKE 'ue_preferences'");

if (!$tableExists || $tableExists->num_rows == 0) {
    echo "<h2>La table ue_preferences n'existe pas</h2>";
} else {
    echo "<h2>La table ue_preferences existe</h2>";
    
    // Afficher la structure de la table
    $columns = $conn->query("SHOW COLUMNS FROM ue_preferences");
    
    if (!$columns) {
        echo "<p>Erreur lors de la récupération des colonnes: " . $conn->error . "</p>";
    } else {
        echo "<h3>Structure de la table:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        while ($column = $columns->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Afficher les premières lignes de données
    $data = $conn->query("SELECT * FROM ue_preferences LIMIT 5");
    
    if (!$data) {
        echo "<p>Erreur lors de la récupération des données: " . $conn->error . "</p>";
    } else {
        echo "<h3>Échantillon de données:</h3>";
        
        if ($data->num_rows == 0) {
            echo "<p>Aucune donnée dans la table</p>";
        } else {
            echo "<table border='1'>";
            
            // En-têtes de colonnes
            $firstRow = $data->fetch_assoc();
            echo "<tr>";
            foreach (array_keys($firstRow) as $key) {
                echo "<th>" . $key . "</th>";
            }
            echo "</tr>";
            
            // Afficher la première ligne
            echo "<tr>";
            foreach ($firstRow as $value) {
                echo "<td>" . ($value ?? "NULL") . "</td>";
            }
            echo "</tr>";
            
            // Afficher les lignes restantes
            while ($row = $data->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . ($value ?? "NULL") . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        }
    }
}

// Fermer la connexion
$conn->close();
?>
