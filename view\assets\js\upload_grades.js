/**
 * JavaScript for the upload_grades page
 *
 * This script handles loading and displaying students and their grades
 * based on the selected filters, as well as saving updated grades.
 */

// Global variables
// currentTeacherId is declared in the HTML file
// If it's not defined yet, we'll initialize it in getCurrentTeacherId()
let studentsData = [];

// Make sure currentTeacherId exists
if (typeof currentTeacherId === 'undefined') {
    var currentTeacherId = null;
}

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Upload Grades page loaded');

    // Get the current teacher ID from the session
    getCurrentTeacherId();

    // Initialize filters
    initializeFilters();

    // Set up event listeners
    setupEventListeners();
});

/**
 * Get the current teacher ID from the session
 */
async function getCurrentTeacherId() {
    try {
        // Check if currentTeacherId is already defined and has a value (from PHP)
        if (currentTeacherId) {
            console.log('Using teacher ID from PHP:', currentTeacherId);

            // Load fields for this teacher
            loadTeacherFields();
            return;
        }

        // If not defined or null, try to get it from the session
        const response = await fetch('../../route/profileRoute.php?action=getCurrentUser');
        const data = await response.json();

        if (data.success && data.user) {
            // Check if teacher_id is available in the session
            if (data.user.teacher_id) {
                currentTeacherId = data.user.teacher_id;
                console.log('Current teacher ID from session:', currentTeacherId);
            }
            // If not, check if id is available
            else if (data.user.id) {
                currentTeacherId = data.user.id;
                console.log('Current teacher ID from API:', currentTeacherId);
            }

            // If we have a teacher ID, load fields
            if (currentTeacherId) {
                loadTeacherFields();
            } else {
                console.error('No teacher ID found in user data:', data.user);
                showError('Failed to get teacher information. Please refresh the page.');
            }
        } else {
            console.error('Failed to get current user:', data);
            showError('Failed to get teacher information. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error getting current teacher ID:', error);
        showError('An error occurred while loading teacher information. Please refresh the page.');
    }
}

/**
 * Initialize filters
 */
function initializeFilters() {
    // Semester and session are static, already in the HTML

    // Other filters will be loaded dynamically based on the teacher
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Field change event
    document.getElementById('filiere').addEventListener('change', function() {
        const filiereId = this.value;
        loadLevels(filiereId);

        // Reset dependent filters
        resetFilter('niveau');
        resetFilter('module');
    });

    // Level change event
    document.getElementById('niveau').addEventListener('change', function() {
        const filiereId = document.getElementById('filiere').value;
        const niveauId = this.value;

        // Reset dependent filters
        resetFilter('semestre');
        resetFilter('module');

        // Load semesters based on field and level
        if (filiereId && niveauId) {
            loadSemestres(filiereId, niveauId);
        }
    });

    // Display button click event
    document.getElementById('display-button').addEventListener('click', function(event) {
        displayStudents(event);
    });

    // Save grades button click event
    document.getElementById('save-grades-button').addEventListener('click', function() {
        saveGrades();
    });
}

/**
 * Load fields (filieres) for the current teacher
 */
async function loadTeacherFields() {
    try {
        if (!currentTeacherId) {
            console.error('No teacher ID available');
            return;
        }

        const response = await fetch(`../../route/enseignantRoute.php?action=getTeacherFields&id=${currentTeacherId}`);
        const data = await response.json();

        if (data.success && data.data) {
            const filiereSelect = document.getElementById('filiere');
            filiereSelect.innerHTML = '<option value="" disabled selected>Select field</option>';

            data.data.forEach(filiere => {
                filiereSelect.innerHTML += `<option value="${filiere.id_filiere}">${filiere.nom_filiere}</option>`;
            });

            console.log('Fields loaded:', data.data.length);
        } else {
            console.error('Failed to load fields:', data);
            showError('Failed to load fields. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error loading fields:', error);
        showError('An error occurred while loading fields. Please refresh the page.');
    }
}

/**
 * Load levels based on the selected field
 *
 * @param {string} filiereId The selected field ID
 */
async function loadLevels(filiereId) {
    try {
        if (!filiereId) {
            console.error('No field ID provided');
            return;
        }

        const response = await fetch(`../../route/niveauRoute.php?id_filiere=${filiereId}`);
        const data = await response.json();

        if (data.data) {
            const niveauSelect = document.getElementById('niveau');
            niveauSelect.innerHTML = '<option value="" disabled selected>Select level</option>';

            data.data.forEach(niveau => {
                niveauSelect.innerHTML += `<option value="${niveau.id_niveau}">${niveau.niveau}</option>`;
            });

            console.log('Levels loaded:', data.data.length);
        } else {
            console.error('Failed to load levels:', data);
            showError('Failed to load levels. Please try again.');
        }
    } catch (error) {
        console.error('Error loading levels:', error);
        showError('An error occurred while loading levels. Please try again.');
    }
}

/**
 * Load semesters based on the selected field and level
 *
 * @param {string} filiereId The selected field ID
 * @param {string} niveauId The selected level ID
 */
async function loadSemestres(filiereId, niveauId) {
    try {
        if (!filiereId || !niveauId) {
            console.error('Missing required parameters for loading semesters');
            return;
        }

        console.log(`Loading semesters for field ID: ${filiereId}, level ID: ${niveauId}`);

        const url = `../../route/semestreRoute.php?action=getSemestresByFiliereAndLevel&filiere_id=${filiereId}&level_id=${niveauId}`;
        console.log('Fetching semesters from URL:', url);

        const response = await fetch(url);
        const data = await response.json();

        if (data.data && Array.isArray(data.data)) {
            const semestreSelect = document.getElementById('semestre');
            semestreSelect.innerHTML = '<option value="" disabled selected>Select semester</option>';

            data.data.forEach(semestre => {
                // Utiliser l'ID du semestre comme valeur et stocker le nom comme attribut data
                const id = semestre.id;
                const name = semestre.nom;
                semestreSelect.innerHTML += `<option value="${id}" data-nom="${name}">${name}</option>`;
            });

            console.log('Semesters loaded:', data.data.length);

            // Add event listener for semester change
            document.getElementById('semestre').addEventListener('change', function() {
                const semestre = this.value;

                if (filiereId && niveauId && semestre) {
                    // Load modules when semester is selected
                    loadModules(filiereId, niveauId, semestre);
                }
            });
        } else {
            console.error('Failed to load semesters or no semesters found:', data);

            if (data.error) {
                showError(`Failed to load semesters: ${data.error}`);
            } else {
                showError('No semesters found for the selected criteria. Please try different filters.');
            }
        }
    } catch (error) {
        console.error('Error loading semesters:', error);
        showError('An error occurred while loading semesters. Please try again.');
    }
}

/**
 * Load modules based on the selected field, level, semester, and teacher
 *
 * @param {string} filiereId The selected field ID
 * @param {string} niveauId The selected level ID
 * @param {string} semestre The selected semester
 */
async function loadModules(filiereId, niveauId, semestre = null) {
    try {
        if (!filiereId || !niveauId || !currentTeacherId || !semestre) {
            console.error('Missing required parameters');
            return;
        }

        console.log(`Loading modules for teacher ID: ${currentTeacherId}, field ID: ${filiereId}, level ID: ${niveauId}, semester: ${semestre}`);

        // Use the same endpoint as in import_grades.php
        let url = `../../controller/importGradesController.php?action=getModules&teacherId=${currentTeacherId}&levelId=${niveauId}&semester=${semestre}&filiere=${filiereId}`;

        console.log('Fetching modules from URL:', url);

        const response = await fetch(url);
        const responseText = await response.text();

        console.log('Raw response:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            showError('Server returned invalid data. Please try again or contact support.');
            return;
        }

        const moduleSelect = document.getElementById('module');
        moduleSelect.innerHTML = '<option value="" disabled selected>Select module</option>';

        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
            console.log('Modules data:', data.data);

            // Determine which fields to use for ID and name
            let idField = 'id';
            let nameField = 'nom';

            // Check if the first module has these fields
            const firstModule = data.data[0];

            if (!firstModule.hasOwnProperty(idField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('id_module')) {
                    idField = 'id_module';
                } else {
                    // Log all available fields for debugging
                    console.log('Available fields in module:', Object.keys(firstModule));

                    // Use the first key as a fallback
                    const keys = Object.keys(firstModule);
                    if (keys.length > 0) {
                        idField = keys[0];
                    }
                }
            }

            if (!firstModule.hasOwnProperty(nameField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('nom_module')) {
                    nameField = 'nom_module';
                } else if (firstModule.hasOwnProperty('name')) {
                    nameField = 'name';
                } else {
                    // Log all available fields for debugging
                    console.log('Available fields in module:', Object.keys(firstModule));

                    // Use the second key as a fallback for the name
                    const keys = Object.keys(firstModule);
                    if (keys.length > 1) {
                        nameField = keys[1];
                    } else if (keys.length > 0) {
                        nameField = keys[0];
                    }
                }
            }

            console.log(`Using ${idField} for module ID and ${nameField} for module name`);

            data.data.forEach(module => {
                const id = module[idField] || '';
                const name = module[nameField] || 'Unknown Module';
                moduleSelect.innerHTML += `<option value="${id}">${name}</option>`;
            });

            console.log('Modules loaded:', data.data.length);
        } else {
            console.error('Failed to load modules or no modules found:', data);

            if (data.error) {
                showError(`Failed to load modules: ${data.error}`);
            } else {
                showError('No modules found for the selected criteria. Please try different filters.');
            }
        }
    } catch (error) {
        console.error('Error loading modules:', error);
        showError('An error occurred while loading modules. Please try again.');
    }
}

/**
 * Reset a filter dropdown
 *
 * @param {string} filterId The ID of the filter to reset
 */
function resetFilter(filterId) {
    const filter = document.getElementById(filterId);
    if (filter) {
        filter.innerHTML = `<option value="" disabled selected>Select ${filterId}</option>`;
    }
}

/**
 * Display students based on the selected filters
 */
async function displayStudents(event) {
    // Empêcher le comportement par défaut si c'est un événement
    if (event) {
        event.preventDefault();
    }

    try {
        // Désactiver le bouton pendant le traitement
        const displayButton = document.getElementById('display-button');
        if (displayButton) {
            displayButton.disabled = true;
            displayButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
        }

        // Get filter values
        const filiereId = document.getElementById('filiere').value;
        const niveauId = document.getElementById('niveau').value;
        const semestre = document.getElementById('semestre').value;
        const session = document.getElementById('session').value;
        const moduleId = document.getElementById('module').value;

        // Validate filters
        if (!filiereId || !niveauId || !semestre || !session || !moduleId) {
            showError('Please select all filters before displaying students.');

            // Réactiver le bouton
            if (displayButton) {
                displayButton.disabled = false;
                displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
            }

            return;
        }

        // Get the text values for the filters
        const filiereName = document.getElementById('filiere').options[document.getElementById('filiere').selectedIndex].text;
        const niveauName = document.getElementById('niveau').options[document.getElementById('niveau').selectedIndex].text;
        const moduleName = document.getElementById('module').options[document.getElementById('module').selectedIndex].text;

        // Récupérer le nom du semestre depuis l'attribut data-nom
        const semestreElement = document.getElementById('semestre');
        const semestreOption = semestreElement.options[semestreElement.selectedIndex];
        const semestreName = semestreOption.getAttribute('data-nom') || semestreOption.text;

        // Log filter values for debugging
        console.log('Filter values:', {
            filiereId, niveauId, semestre, session, moduleId,
            filiereName, niveauName, moduleName, semestreName
        });

        // Redirect to student_grades.php with all filter parameters
        // Utiliser encodeURIComponent pour tous les paramètres
        const url = `student_grades.php?filiere=${encodeURIComponent(filiereId)}&niveau=${encodeURIComponent(niveauId)}&semestre=${encodeURIComponent(semestre)}&session=${encodeURIComponent(session)}&module=${encodeURIComponent(moduleId)}&filiere_name=${encodeURIComponent(filiereName)}&niveau_name=${encodeURIComponent(niveauName)}&module_name=${encodeURIComponent(moduleName)}&semestre_name=${encodeURIComponent(semestreName)}`;

        console.log('Redirecting to:', url);

        // Utiliser window.location.href pour une redirection standard
        window.location.href = url;
    } catch (error) {
        console.error('Error displaying students:', error);
        showError('An error occurred while loading students. Please try again.');

        // Réactiver le bouton en cas d'erreur
        const displayButton = document.getElementById('display-button');
        if (displayButton) {
            displayButton.disabled = false;
            displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
        }
    }
}

/**
 * Display applied filters
 *
 * @param {Object} filters The applied filters
 */
function displayAppliedFilters(filters) {
    const appliedFiltersContainer = document.getElementById('applied-filters');
    appliedFiltersContainer.innerHTML = '';

    for (const [key, value] of Object.entries(filters)) {
        if (value) {
            const filterLabel = key.charAt(0).toUpperCase() + key.slice(1);
            appliedFiltersContainer.innerHTML += `
                <div class="filter-badge">
                    <span class="filter-badge-label">${filterLabel}:</span>
                    <span>${value}</span>
                </div>
            `;
        }
    }

    document.getElementById('applied-filters-card').style.display = 'block';
}

/**
 * Render the students table
 *
 * @param {Array} students The students data
 */
function renderStudentsTable(students) {
    const studentsListContainer = document.getElementById('students-list');
    studentsListContainer.innerHTML = '';

    if (students.length === 0) {
        document.getElementById('grades-table-card').style.display = 'none';
        document.getElementById('no-students-message').style.display = 'block';
        return;
    }

    students.forEach(student => {
        studentsListContainer.innerHTML += `
            <tr>
                <td>${student.CNE}</td>
                <td>${student.nom}</td>
                <td>${student.prenom}</td>
                <td>
                    <input type="number" class="grade-input"
                           data-student-id="${student.id_etudiant}"
                           value="${student.valeur || ''}"
                           min="0" max="20" step="0.25">
                </td>
            </tr>
        `;
    });

    document.getElementById('grades-table-card').style.display = 'block';
    document.getElementById('no-students-message').style.display = 'none';
}

/**
 * Save the updated grades
 */
async function saveGrades() {
    try {
        // Get all grade inputs
        const gradeInputs = document.querySelectorAll('.grade-input');

        // Get filter values
        const moduleId = document.getElementById('module').value;
        const niveauId = document.getElementById('niveau').value;
        const semestre = document.getElementById('semestre').value;
        const session = document.getElementById('session').value;

        // Prepare grades data
        const gradesData = [];

        gradeInputs.forEach(input => {
            const studentId = input.getAttribute('data-student-id');
            const value = input.value;

            if (studentId && value) {
                gradesData.push({
                    id_etudiant: studentId,
                    id_module: moduleId,
                    id_niveau: niveauId,
                    semestre: semestre,
                    session: session,
                    valeur: parseFloat(value)
                });
            }
        });

        if (gradesData.length === 0) {
            showError('No grades to save.');
            return;
        }

        // Send grades data to the server
        const response = await fetch('../../route/noteRoute.php?action=saveGrades', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ grades: gradesData })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('Grades saved successfully!');
        } else {
            console.error('Failed to save grades:', data);
            showError('Failed to save grades. Please try again.');
        }
    } catch (error) {
        console.error('Error saving grades:', error);
        showError('An error occurred while saving grades. Please try again.');
    }
}

/**
 * Show an error message
 *
 * @param {string} message The error message
 */
function showError(message) {
    alert(message);
}

/**
 * Show a success message
 *
 * @param {string} message The success message
 */
function showSuccess(message) {
    alert(message);
}