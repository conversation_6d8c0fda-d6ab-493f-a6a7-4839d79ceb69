/* Student Grades Page Styles */

/* University Header */
.university-header {
    background-color: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.university-logo {
    width: 80px;
    margin-bottom: 10px;
}

.university-name {
    color: #3f51b5;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.university-school {
    color: #666;
    font-size: 1rem;
}

/* Filters Container */
.filters-container {
    background: linear-gradient(135deg, #f5f7ff 0%, #f0f4ff 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.filter-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.filter-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    margin-right: 15px;
    font-size: 1.2rem;
    color: white;
}

.filter-card:nth-child(1) .filter-icon,
.filter-card:nth-child(4) .filter-icon {
    background: linear-gradient(135deg, #4e54c8, #8f94fb);
}

.filter-card:nth-child(2) .filter-icon,
.filter-card:nth-child(5) .filter-icon {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

.filter-card:nth-child(3) .filter-icon,
.filter-card:nth-child(6) .filter-icon {
    background: linear-gradient(135deg, #fc4a1a, #f7b733);
}

.filter-content {
    flex: 1;
}

.filter-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
}

.filter-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Special Cards */
.module-card {
    background: linear-gradient(to right, #e6eef3, #faeafe);
}

.module-card .filter-icon {
    background: linear-gradient(135deg, #6a3093, #a044ff);
}

.coordinator-card {
    background: linear-gradient(135deg, #e0f7fa, #d4e6f9);
}

.coordinator-card .filter-icon {
    background: linear-gradient(135deg, #1e88e5, #64b5f6);
}

.teacher-card {
    background: linear-gradient(to right, #e6eef3, #faeafe);;
}

.teacher-card .filter-icon {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
}

/* Grades Container */
.grades-container {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin-top: 30px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.grades-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.grades-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
}

.grades-actions {
    display: flex;
    gap: 15px;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 8px 15px 8px 35px;
    border-radius: 20px;
    border: 1px solid #ddd;
    width: 250px;
    font-size: 0.9rem;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

#download-pdf {
    background: linear-gradient(135deg, #6a3093, #a044ff);
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 0.9rem;
    transition: all 0.3s;
}

#download-pdf:hover {
    background: linear-gradient(135deg, #5a2583, #9034ef);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(106, 48, 147, 0.3);
}

.grades-description {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

/* Top Performances */
.top-performances {
    background: linear-gradient(135deg, #fff9f9, #fff5f5);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
}

.top-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.top-students {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.top-student {
    flex: 1;
    min-width: 200px;
    background: white;
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.student-rank {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #ff9a9e, #fad0c4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-right: 12px;
}

.top-student:nth-child(1) .student-rank {
    background: linear-gradient(135deg, #ffd700, #ffb347);
}

.top-student:nth-child(2) .student-rank {
    background: linear-gradient(135deg, #c0c0c0, #e0e0e0);
}

.top-student:nth-child(3) .student-rank {
    background: linear-gradient(135deg, #cd7f32, #e9967a);
}

.student-info {
    flex: 1;
}

.student-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.student-grade {
    color: #4caf50;
    font-weight: bold;
}

/* Grades Table */
.grades-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 20px;
}

.grades-table thead th {
    background-color: #f5f7ff;
    color: #333;
    font-weight: 600;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #e0e0e0;
}

.grades-table thead th:first-child {
    border-top-left-radius: 10px;
}

.grades-table thead th:last-child {
    border-top-right-radius: 10px;
}

.grades-table tbody tr {
    transition: background-color 0.2s;
}

.grades-table tbody tr:hover {
    background-color: #f9f9f9;
}

.grades-table tbody td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    color: #333;
}

.grades-table tbody tr:last-child td {
    border-bottom: none;
}

.grades-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 10px;
}

.grades-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 10px;
}

.editable-grade {
    cursor: pointer;
    padding: 2px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.editable-grade:hover {
    background-color: #f0f0f0;
}

.grade-input {
    width: 60px;
    padding: 2px 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.validation-status {
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 4px;
}

.validation-status.validated {
    color: #4caf50;
    background-color: rgba(76, 175, 80, 0.1);
}

.validation-status.rejected {
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
}

/* No Students Message */
.no-students-message {
    text-align: center;
    padding: 30px;
    color: #666;
    font-style: italic;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background-color: #f5f7ff;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    color: #333;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #e0e4f5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
}

.page-number:hover {
    background-color: #e0e4f5;
}

.page-number.active {
    background-color: #3f51b5;
    color: white;
}

.total-students {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .grades-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .grades-actions {
        width: 100%;
        flex-direction: column;
        gap: 10px;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .top-students {
        flex-direction: column;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
}