<?php
function getConnection() {
    // Désactiver l'affichage des erreurs
    $displayErrors = ini_get('display_errors');
    ini_set('display_errors', 0);

    try {
        $conn = mysqli_connect("localhost", "root", "", "ensah");

        if (!$conn) {
            error_log("Database connection failed: " . mysqli_connect_error());
            return null;
        }

        // Restaurer le paramètre d'affichage des erreurs
        ini_set('display_errors', $displayErrors);

        return $conn;
    } catch (Exception $e) {
        error_log("Exception in getConnection: " . $e->getMessage());

        // Restaurer le paramètre d'affichage des erreurs
        ini_set('display_errors', $displayErrors);

        return null;
    }
}
?>
