<?php
// Include the authentication check for teachers
require_once '../includes/auth_check_enseignant.php';

// Récupérer les informations de l'enseignant depuis la session
$userName = $_SESSION['user']['username'] ?? 'Enseignant';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';
$specialtyName = $_SESSION['user']['specialty_name'] ?? 'Non spécifié';
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer les paramètres de l'URL
$filiereId = isset($_GET['filiere']) ? trim(urldecode($_GET['filiere'])) : '';
$niveauId = isset($_GET['niveau']) ? trim(urldecode($_GET['niveau'])) : '';
$semestre = isset($_GET['semestre']) ? trim(urldecode($_GET['semestre'])) : '';
$session = isset($_GET['session']) ? trim(urldecode($_GET['session'])) : '';
$moduleId = isset($_GET['module']) ? trim(urldecode($_GET['module'])) : '';

// Récupérer les noms des filtres sélectionnés
$filiereName = isset($_GET['filiere_name']) ? trim(urldecode($_GET['filiere_name'])) : '';
$niveauName = isset($_GET['niveau_name']) ? trim(urldecode($_GET['niveau_name'])) : '';
$moduleName = isset($_GET['module_name']) ? trim(urldecode($_GET['module_name'])) : '';
$semestreName = isset($_GET['semestre_name']) ? trim(urldecode($_GET['semestre_name'])) : $semestre; // Utiliser l'ID comme fallback

// Log des paramètres bruts pour débogage
error_log("Paramètres URL bruts: filiere=" . (isset($_GET['filiere']) ? $_GET['filiere'] : 'non défini'));
error_log("Paramètres URL bruts: niveau=" . (isset($_GET['niveau']) ? $_GET['niveau'] : 'non défini'));
error_log("Paramètres URL bruts: semestre=" . (isset($_GET['semestre']) ? $_GET['semestre'] : 'non défini'));
error_log("Paramètres URL bruts: session=" . (isset($_GET['session']) ? $_GET['session'] : 'non défini'));
error_log("Paramètres URL bruts: module=" . (isset($_GET['module']) ? $_GET['module'] : 'non défini'));

// Log des paramètres décodés pour débogage
error_log("Paramètres URL décodés: filiereId=$filiereId, niveauId=$niveauId, semestre=$semestre, session=$session, moduleId=$moduleId");
error_log("Noms des filtres: filiereName=$filiereName, niveauName=$niveauName, moduleName=$moduleName");

// Vérifier si les paramètres sont vides et afficher un message d'erreur
$parametresManquants = [];
if (empty($filiereId)) $parametresManquants[] = 'filiere';
if (empty($niveauId)) $parametresManquants[] = 'niveau';
if (empty($semestre)) $parametresManquants[] = 'semestre';
if (empty($session)) $parametresManquants[] = 'session';
if (empty($moduleId)) $parametresManquants[] = 'module';

if (!empty($parametresManquants)) {
    error_log("ATTENTION: Paramètres manquants: " . implode(', ', $parametresManquants));
}

// Définir les variables JavaScript pour les paramètres
echo "<script>
    // Paramètres de filtrage
    var urlFiliereId = " . json_encode($filiereId) . ";
    var urlNiveauId = " . json_encode($niveauId) . ";
    var urlSemestre = " . json_encode($semestre) . ";
    var urlSession = " . json_encode($session) . ";
    var urlModuleId = " . json_encode($moduleId) . ";

    // Noms des filtres
    var urlFiliereName = " . json_encode($filiereName) . ";
    var urlNiveauName = " . json_encode($niveauName) . ";
    var urlModuleName = " . json_encode($moduleName) . ";
    var urlSemestreName = " . json_encode($semestreName) . ";

    // Log des paramètres pour débogage
    console.log('Paramètres URL définis dans PHP:', {
        filiereId: urlFiliereId,
        niveauId: urlNiveauId,
        semestre: urlSemestre,
        session: urlSession,
        moduleId: urlModuleId,
        filiereName: urlFiliereName,
        niveauName: urlNiveauName,
        moduleName: urlModuleName,
        semestreName: urlSemestreName
    });

    // Vérifier si les paramètres sont valides
    var parametresValides = urlFiliereId && urlNiveauId && urlSemestre && urlSession && urlModuleId;
    console.log('Paramètres valides:', parametresValides);

    // Ajouter l'ID de l'enseignant pour l'envoi au coordinateur
    var teacherId = " . json_encode($teacherId) . ";
    console.log('ID de l\'enseignant:', teacherId);
</script>";

// Année académique actuelle (à remplacer par une valeur dynamique si nécessaire)
$academicYear = "2024/2025";

// Récupérer le cycle depuis la base de données
require_once '../../model/cycleModel.php';
require_once '../../model/niveauModel.php';
require_once '../../model/filiereModel.php';

// Déterminer le cycle en fonction du niveau
$cycle = "Cycle Ingénieur"; // Valeur par défaut

if (!empty($niveauId)) {
    // Récupérer le niveau pour obtenir le cycle_id
    $niveauInfo = getNiveauById($niveauId);

    if ($niveauInfo && isset($niveauInfo['cycle_id'])) {
        $cycleId = $niveauInfo['cycle_id'];
        $cycleInfo = getCycleById($cycleId);

        if ($cycleInfo && isset($cycleInfo['nom'])) {
            $cycle = $cycleInfo['nom'];
        }
    }
} else if (!empty($filiereId)) {
    // Si pas de niveau mais une filière, récupérer le cycle depuis la filière
    $filiereInfo = getFiliereById($filiereId);

    if ($filiereInfo && isset($filiereInfo['id_cycle'])) {
        $cycleId = $filiereInfo['id_cycle'];
        $cycleInfo = getCycleById($cycleId);

        if ($cycleInfo && isset($cycleInfo['nom'])) {
            $cycle = $cycleInfo['nom'];
        }
    }
}

// Récupérer les informations du coordinateur et de l'enseignant
require_once '../../model/moduleModel.php';

// Add error handling for the module retrieval
try {
    $moduleInfo = getModuleById($moduleId);

    // Log the module info for debugging
    error_log("Module info: " . print_r($moduleInfo, true));

    $coordinateur = "Dr. Mohammed Amine"; // Valeur par défaut
    $enseignantModule = "$prenom $nom"; // Enseignant connecté

    if ($moduleInfo && isset($moduleInfo['coordinateur']) && !empty($moduleInfo['coordinateur'])) {
        $coordinateur = $moduleInfo['coordinateur'];
    }

    if ($moduleInfo && isset($moduleInfo['enseignant']) && !empty($moduleInfo['enseignant'])) {
        $enseignantModule = $moduleInfo['enseignant'];
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error retrieving module info: " . $e->getMessage());

    // Set default values
    $coordinateur = "Dr. Mohammed Amine";
    $enseignantModule = "$prenom $nom";
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes des Étudiants - Tableau de bord Enseignant</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/student_grades.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include_once '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <?php include_once '../includes/header.php'; ?>

            <!-- Page Content -->
            <div class="container-fluid p-4">
                <!-- University Header -->
                <div class="university-header mb-4">
                    <div class="text-center">
                        <img src="../assets/img/logo.png" alt="Logo Université" class="university-logo">
                        <h2 class="university-name">Université Abdelmalek Essaadi</h2>
                        <p class="university-school">École Nationale des Sciences Appliquées d'Al Hoceima</p>
                    </div>
                </div>

                <!-- Applied Filters Cards -->
                <div class="filters-container mb-4">
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3">
                        <!-- Academic Year -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-calendar-date"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">AU</div>
                                    <div class="filter-value" id="academic-year-value"><?php echo $academicYear; ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Niveau -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-bar-chart-steps"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Niveau</div>
                                    <div class="filter-value" id="niveau-value"><?php echo $niveauName; ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Cycle -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-diagram-3"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Cycle</div>
                                    <div class="filter-value" id="cycle-value"><?php echo $cycle; ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Filière -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-mortarboard"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Filière</div>
                                    <div class="filter-value" id="filiere-value"><?php echo $filiereName; ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Session -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-calendar-check"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Session</div>
                                    <div class="filter-value" id="session-value"><?php echo ucfirst($session); ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Semestre -->
                        <div class="col">
                            <div class="filter-card">
                                <div class="filter-icon">
                                    <i class="bi bi-calendar-week"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Semestre</div>
                                    <div class="filter-value" id="semestre-value"><?php echo $semestreName; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <!-- Module (full width) -->
                        <div class="col-12">
                            <div class="filter-card module-card">
                                <div class="filter-icon">
                                    <i class="bi bi-book"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Module</div>
                                    <div class="filter-value" id="module-value"><?php echo $moduleName; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <!-- Coordinateur -->
                        <div class="col-md-6">
                            <div class="filter-card coordinator-card">
                                <div class="filter-icon">
                                    <i class="bi bi-person-badge"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Coordinateur</div>
                                    <div class="filter-value"><?php echo $coordinateur; ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- Enseignant -->
                        <div class="col-md-6">
                            <div class="filter-card teacher-card">
                                <div class="filter-icon">
                                    <i class="bi bi-person-video3"></i>
                                </div>
                                <div class="filter-content">
                                    <div class="filter-label">Enseignant(s)</div>
                                    <div class="filter-value"><?php echo $enseignantModule; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Grades Table -->
                <div class="grades-container">
                    <div class="grades-header">
                        <div class="grades-title">
                            <i class="bi bi-list-check me-2"></i>
                            Liste des Notes
                        </div>
                        <div class="grades-actions">
                            <div class="search-box">
                                <input type="text" id="search-input" placeholder="Rechercher par nom, prénom...">
                                <i class="bi bi-search"></i>
                            </div>
                            <!-- <button id="save-all-grades" class="btn btn-success me-2">
                                <i class="bi bi-save me-2"></i>Enregistrer toutes les notes
                            </button> -->
                            <button id="download-pdf" class="btn btn-primary me-2">
                                <i class="bi bi-file-earmark-pdf me-2"></i>Télécharger PDF
                            </button>
                            <button id="send-to-coordinator" class="btn btn-info">
                                <i class="bi bi-send me-2"></i>Export Grades
                            </button>
                        </div>
                    </div>

                    <div class="grades-description">
                        Liste complète des notes des étudiants pour ce module.
                    </div>

                    <!-- Top Performances -->
                    <div class="top-performances">
                        <div class="top-title">
                            <i class="bi bi-star-fill text-warning me-2"></i>
                            Top Performances
                        </div>
                        <div class="top-students">
                            <div class="top-student">
                                <div class="student-rank">1</div>
                                <div class="student-info">
                                    <div class="student-name">Ahmed Alaoui</div>
                                    <div class="student-grade">19.5</div>
                                </div>
                            </div>
                            <div class="top-student">
                                <div class="student-rank">2</div>
                                <div class="student-info">
                                    <div class="student-name">Sara Bennani</div>
                                    <div class="student-grade">18.75</div>
                                </div>
                            </div>
                            <div class="top-student">
                                <div class="student-rank">3</div>
                                <div class="student-info">
                                    <div class="student-name">Karim Tazi</div>
                                    <div class="student-grade">18.25</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Grades Table -->
                    <div class="table-responsive">
                        <table class="table grades-table" id="grades-table">
                            <thead>
                                <tr>
                                    <th>N°</th>
                                    <th>CNE</th>
                                    <th>Nom</th>
                                    <th>Prénom</th>
                                    <th>Moyenne</th>
                                    <th>V/R</th>
                                </tr>
                            </thead>
                            <tbody id="students-list">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- No Students Message -->
                    <div class="no-students-message" id="no-students-message" style="display: none;">
                        Aucune note trouvée pour les filtres sélectionnés
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div class="pagination">
                            <button class="pagination-btn" id="prev-page" disabled>
                                <i class="bi bi-chevron-left"></i> Précédent
                            </button>
                            <div class="pagination-pages" id="pagination-pages">
                                <span class="page-number active">1</span>
                            </div>
                            <button class="pagination-btn" id="next-page">
                                Suivant <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="total-students" id="total-students">
                            Total: 0 étudiants
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- html2pdf.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/student_grades.js"></script>
</body>
</html>