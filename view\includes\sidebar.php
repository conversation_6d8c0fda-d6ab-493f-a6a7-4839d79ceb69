<?php
$currentPage = basename($_SERVER['PHP_SELF']); // Récupère le fichier actuel

// Récupérer le rôle de l'utilisateur depuis la session
$userRole = isset($_SESSION['user']['role']) ? $_SESSION['user']['role'] : 'admin';

// Tableau des éléments du menu pour les administrateurs
$adminMenuItems = [
    "Dashboard" => [
        "link" => "dashboard.php",
        "icon" => "bi-house-door",
        "submenu" => null
    ],
    "User Management" => [
        "link" => "#",
        "icon" => "bi-people",
        "submenu" => [
            "Students" => "students.php",
            "Faculty" => "faculty.php",
            "Staff" => "staff.php"
        ]
    ],
    "Content Management" => [
        "link" => "#",
        "icon" => "bi-book",
        "submenu" => [
            "Modules" => "modules.php",
            "Demandes" => "demandes.php",
            "Resources" => "resources.php"
        ]
    ],
    "Timetable Management" => [
        "link" => "#",
        "icon" => "bi-calendar-week",
        "submenu" => [
            "Class Schedule" => "timetable.php",
            "Exam Schedule" => "exam_schedule.php"
        ]
    ],
    "Para-scolaire" => [
        "link" => "#",
        "icon" => "bi-bar-chart",
        "submenu" => [
            "Clubs" => "clubs.php",
            "Events" => "events.php"
        ]
    ],
    "Communication" => [
        "link" => "#",
        "icon" => "bi-chat-square-text",
        "submenu" => [
            "Messages" => "messages.php",
            "Announcements" => "notifications.php",
            "Personnel" => "personnel.php"
        ]
    ],
    "Settings" => [
        "link" => "#",
        "icon" => "bi-gear",
        "submenu" => [
            "General" => "settings.php",
            "Comptes utilisateurs" => "user-accounts.php"
        ]
    ]
];

// Tableau des éléments du menu pour les chefs de département
$departmentHeadMenuItems = [
    "Dashboard" => [
        "link" => "dashboard.php",
        "icon" => "bi-house-door",
        "submenu" => null
    ],
    "Modules Management" => [
        "link" => "#",
        "icon" => "bi-book",
        "submenu" => [
            "View Department Modules" => "listerUEdepart.php",
            "View Department Professors" => "department_professors.php",
            "Assign Modules to Professors" => "assign_modules.php",
            "Manual Assignment" => "manual_assignment.php",
            "UE Requests" => "ue_requests.php",
            "View Affectations" => "ListerAffUECord.php",
            "View Unassigned Modules" => "unassigned_modules.php",
            "View Professors' Selections" => "professors_selections.php",
            "Generate Teaching Load" => "teaching_load.php"
        ]
    ],
    "History" => [
        "link" => "history.php",
        "icon" => "bi-clock-history",
        "submenu" => null
    ],
    "Reporting" => [
        "link" => "reporting.php",
        "icon" => "bi-file-earmark-text",
        "submenu" => null
    ],
    "Data Import/Export" => [
        "link" => "data_import_export.php",
        "icon" => "bi-arrow-down-up",
        "submenu" => null
    ],
    "Department Settings" => [
        "link" => "department_settings.php",
        "icon" => "bi-gear",
        "submenu" => null
    ],
    "Communication" => [
        "link" => "#",
        "icon" => "bi-chat-square-text",
        "submenu" => [
            "Messages" => "messages.php",
            "Announcements" => "notifications.php",
            "Personnel" => "personnel.php"
        ]
    ]
];

// Tableau des éléments du menu pour les coordinateurs de programme
$programCoordinatorMenuItems = [
    "Dashboard" => [
        "link" => "dashboard.php",
        "icon" => "bi-house-door",
        "submenu" => null
    ],
    "Curriculum Management" => [
        "link" => "#",
        "icon" => "bi-journal-text",
        "submenu" => [
            "Create/Import Course Descriptions" => "descriptif.php",
            "View Program Modules" => "listerUEfiliere.php",
            "Define TD/TP Groups" => "define_groups_simple.php",
            "View Teaching Assignments" => "ListerAffUECord.php",
            "Assign Modules to Adjuncts" => "assign_adjuncts.php",
            "Create Vacataire Accounts" => "create-vacataire-account.php",

        ]
    ],
    "Schedule Management" => [
        "link" => "#",
        "icon" => "bi-calendar-week",
        "submenu" => [
            "Upload and Assign Timetables" => "upload_timetables.php"
        ]
    ],
    "History" => [
        "link" => "history.php",
        "icon" => "bi-clock-history",
        "submenu" => null
    ],
    "Data Import Export" => [
        "link" => "#",
        "icon" => "bi-arrow-down-up",
        "submenu" => [
            "Import Grades" => "import_grades.php"
        ]
    ],
    "Program Settings" => [
        "link" => "program_settings.php",
        "icon" => "bi-gear",
        "submenu" => null
    ],
    "Communication" => [
        "link" => "#",
        "icon" => "bi-chat-square-text",
        "submenu" => [
            "Messages" => "messages.php",
            "Announcements" => "notifications.php",
            "Personnel" => "personnel.php"
        ]
    ]
];

// Tableau des éléments du menu pour les professeurs
$professorMenuItems = [
    "Dashboard" => [
        "link" => "dashboard.php",
        "icon" => "bi-house-door",
        "submenu" => null
    ],
    "Teaching Preferences" => [
        "link" => "ue_preferences.php",
        "icon" => "bi-list-check",
        "submenu" => null
    ],
    "Assigned Modules" => [
        "link" => "#",
        "icon" => "bi-book",
        "submenu" => [
            "View Assigned Modules" => "assigned_modules.php",
            "Upload Grades" => "upload_grades.php"
        ]
    ],
    "History" => [
        "link" => "history.php",
        "icon" => "bi-clock-history",
        "submenu" => null
    ],
    "Communication" => [
        "link" => "#",
        "icon" => "bi-chat-square-text",
        "submenu" => [
            "Messages" => "messages.php",
            "Announcements" => "notifications.php",
            "Personnel" => "personnel.php"
        ]
    ]
];

// Sélectionner le menu approprié en fonction du rôle
switch ($userRole) {
    case 'chef de departement':
        $menuItems = $departmentHeadMenuItems;
        break;
    case 'coordinateur':
        $menuItems = $programCoordinatorMenuItems;
        break;
    case 'enseignant':
        $menuItems = $professorMenuItems;
        break;
    case 'vacataire':
        $menuItems = $professorMenuItems; // Les vacataires utilisent le même menu que les enseignants
        break;
    case 'admin':
        $menuItems = $adminMenuItems;
        break;
    default:
        $menuItems = $adminMenuItems;
        break;
};
?>

<!-- Sidebar -->
<div class="sidebar">
       <div class="sidebar-header border-bottom border-sidebar p-4">
          <a href="index.html" class="d-flex align-items-center text-decoration-none">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="h-6 w-6 mr-2 text-primary"
            >
              <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
              <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"/>
            </svg>
            <span class="ms-2 fs-4 fw-bold text-primary">UniAdmin</span>
          </a>
        </div>

    <div class="sidebar-menu py-2">
        <?php foreach ($menuItems as $label => $item): ?>
            <?php if ($item['submenu'] === null): ?>
                <!-- Élément simple sans sous-menu -->
                <div class="sidebar-item <?= ($currentPage == $item['link']) ? 'active' : '' ?>">
                    <a href="<?= $item['link'] ?>" class="nav-link">
                        <i class="bi <?= $item['icon'] ?>"></i>
                        <span class="ms-2"><?= $label ?></span>
                    </a>
                </div>
            <?php else: ?>
                <!-- Élément avec sous-menu -->
                <div class="sidebar-item">
                    <button class="nav-link w-100 d-flex justify-content-between align-items-center"
                            data-bs-toggle="collapse"
                            data-bs-target="#<?= str_replace(' ', '', $label) ?>Menu"
                            aria-expanded="<?= in_array($currentPage, $item['submenu']) ? 'true' : 'false' ?>">
                        <div class="d-flex align-items-center">
                            <i class="bi <?= $item['icon'] ?>"></i>
                            <span class="ms-2"><?= $label ?></span>
                        </div>
                        <i class="bi bi-chevron-down submenu-icon"></i>
                    </button>
                    <div class="collapse submenu <?= in_array($currentPage, $item['submenu']) ? 'show' : '' ?>" id="<?= str_replace(' ', '', $label) ?>Menu">
                        <?php foreach ($item['submenu'] as $subLabel => $subLink): ?>
                            <div class="sidebar-item <?= ($currentPage == $subLink) ? 'active' : '' ?>">
                                <a href="<?= $subLink ?>" class="nav-link"><?= $subLabel ?></a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>

<!-- We're using a single responsive sidebar now -->