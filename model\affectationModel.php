<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Ensure the ue_preferences table exists and has the correct structure
 *
 * @return bool True if the table exists with the correct structure, false otherwise
 */
function ensureUePreferencesTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureUePreferencesTable");
        return false;
    }

    // Check if the table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'ue_preferences'");

    if (!$tableExists) {
        error_log("Error checking if ue_preferences table exists: " . $conn->error);
        $conn->close();
        return false;
    }

    // Check if the table exists
    $exists = $tableExists->num_rows > 0;

    if (!$exists) {
        error_log("ue_preferences table does not exist, creating it now");

        // Create the table
        $createTableSQL = "CREATE TABLE `ue_preferences` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `id_enseignant` int(11) NOT NULL,
            `id_ue` int(11) NOT NULL,
            `reason` text COLLATE utf8mb4_general_ci DEFAULT NULL,
            `statut` ENUM('en_attente', 'acceptee', 'rejetee') NOT NULL DEFAULT 'en_attente',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_enseignant_ue` (`id_enseignant`, `id_ue`),
            KEY `fk_ue_preferences_enseignant` (`id_enseignant`),
            KEY `fk_ue_preferences_ue` (`id_ue`),
            CONSTRAINT `fk_ue_preferences_enseignant` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT `fk_ue_preferences_ue` FOREIGN KEY (`id_ue`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $result = $conn->query($createTableSQL);

        if (!$result) {
            error_log("Error creating ue_preferences table: " . $conn->error);
            $conn->close();
            return false;
        }

        error_log("ue_preferences table created successfully");
        $conn->close();
        return true;
    } else {
        error_log("ue_preferences table exists");
        $conn->close();
        return true;
    }
}

/**
 * Ensure the ue_preferences table has a statut column
 *
 * @return bool True if the column exists or was created successfully, false otherwise
 */
function ensureUePreferencesStatusColumn() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureUePreferencesStatusColumn");
        return false;
    }

    // Check if the column exists
    $columnExists = $conn->query("SHOW COLUMNS FROM ue_preferences LIKE 'statut'");

    if (!$columnExists) {
        error_log("Error checking if statut column exists: " . $conn->error);
        $conn->close();
        return false;
    }

    // Check if the column exists
    $exists = $columnExists->num_rows > 0;

    if (!$exists) {
        error_log("statut column does not exist in ue_preferences table, creating it now");

        // Add the column
        $addColumnSQL = "ALTER TABLE ue_preferences ADD COLUMN statut ENUM('en_attente', 'acceptee', 'rejetee') NOT NULL DEFAULT 'en_attente'";

        $result = $conn->query($addColumnSQL);

        if (!$result) {
            error_log("Error adding statut column to ue_preferences table: " . $conn->error);
            $conn->close();
            return false;
        }

        error_log("statut column added successfully to ue_preferences table");
        $conn->close();
        return true;
    } else {
        error_log("statut column exists in ue_preferences table");
        $conn->close();
        return true;
    }
}

/**
 * Ensure the affectation table exists, create it if it doesn't
 *
 * @return bool True if the table exists or was created successfully, false otherwise
 */
function ensureAffectationTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureAffectationTable");
        return false;
    }

    // Check if the table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'affectation'");

    if (!$tableExists) {
        error_log("Error checking if affectation table exists: " . $conn->error);
        $conn->close();
        return false;
    }

    // Check if the table exists
    $exists = $tableExists->num_rows > 0;

    if (!$exists) {
        error_log("affectation table does not exist, creating it now");

        // Create the table with the correct column names
        $createTableSQL = "CREATE TABLE `affectation` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `professeur_id` int(11) NOT NULL,
            `unite_enseignement_id` int(11) NOT NULL,
            `annee_academique` varchar(9) COLLATE utf8mb4_general_ci NOT NULL,
            `statut` enum('acceptee','rejetee') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'acceptee',
            `commentaire` text COLLATE utf8mb4_general_ci DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_enseignant_ue_annee` (`professeur_id`, `unite_enseignement_id`, `annee_academique`),
            KEY `fk_affectation_enseignant` (`professeur_id`),
            KEY `fk_affectation_ue` (`unite_enseignement_id`),
            CONSTRAINT `fk_affectation_enseignant` FOREIGN KEY (`professeur_id`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT `fk_affectation_ue` FOREIGN KEY (`unite_enseignement_id`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $result = $conn->query($createTableSQL);

        if (!$result) {
            error_log("Error creating affectation table: " . $conn->error);
            $conn->close();
            return false;
        }

        error_log("affectation table created successfully");
        $conn->close();
        return true;
    } else {
        // Table exists, check if it has the correct structure
        $columnsCheck = $conn->query("SHOW COLUMNS FROM affectation");
        $columns = [];
        while ($column = $columnsCheck->fetch_assoc()) {
            $columns[] = $column['Field'];
        }
        error_log("Existing affectation columns: " . implode(", ", $columns));

        // Check if the table has the required columns with the correct names
        $hasCorrectStructure = in_array('professeur_id', $columns) &&
                              in_array('unite_enseignement_id', $columns) &&
                              in_array('annee_academique', $columns);

        if (!$hasCorrectStructure) {
            error_log("affectation table exists but has incorrect structure. Expected columns: professeur_id, unite_enseignement_id, annee_academique");
            error_log("Found columns: " . implode(", ", $columns));
            // We won't modify the existing table structure automatically to avoid data loss
            // Instead, we'll just log a warning and continue
        }

        $conn->close();
        return true;
    }
}

/**
 * Get all UE preferences for a specific department
 *
 * @param int $departmentId The department ID
 * @return array Array of UE preferences for the department
 */
function getUePreferencesByDepartment($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getUePreferencesByDepartment");
        return ["error" => "Database connection error"];
    }

    // Ensure the ue_preferences table has a statut column
    if (!ensureUePreferencesStatusColumn()) {
        error_log("Failed to ensure ue_preferences table has statut column");
        // Continue anyway, the query will fail if the column doesn't exist
    }

    // Validate department ID
    if (empty($departmentId)) {
        error_log("Empty department ID provided to getUePreferencesByDepartment");
        return ["error" => "Department ID cannot be empty"];
    }

    // Ensure the department ID is safe
    $departmentId = mysqli_real_escape_string($conn, $departmentId);

    $sql = "SELECT up.*, up.id as preference_id,
            ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
            e.nom as enseignant_nom, e.prenom as enseignant_prenom, e.id_enseignant, e.CNI as enseignant_cni,
            DATE_FORMAT(up.created_at, '%d/%m/%Y') as date_demande
            FROM ue_preferences up
            LEFT JOIN uniteenseignement ue ON up.id_ue = ue.id
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN enseignant e ON up.id_enseignant = e.id_enseignant
            WHERE f.id_dep = '$departmentId'
            ORDER BY up.created_at DESC, m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUePreferencesByDepartment: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching preferences: " . $error];
    }

    $preferences = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $preferences[] = $row;
    }

    mysqli_close($conn);
    return $preferences;
}

/**
 * Accept a UE preference and create an affectation
 *
 * @param int $preferenceId The preference ID
 * @param string $academicYear The academic year
 * @param string $commentaire Optional comment
 * @param int $departmentId The department ID of the chef de département (for authorization)
 * @return bool|array True on success, error array on failure
 */
function acceptUePreference($preferenceId, $academicYear, $commentaire = null, $departmentId = null) {
    // Debug log
    error_log("acceptUePreference called with: preferenceId=$preferenceId, academicYear=$academicYear, commentaire=" . ($commentaire ?? 'null') . ", departmentId=" . ($departmentId ?? 'null'));

    // Validate parameters
    if (empty($preferenceId)) {
        error_log("Empty preference ID provided to acceptUePreference");
        return ["error" => "Preference ID cannot be empty"];
    }

    if (empty($academicYear)) {
        error_log("Empty academic year provided to acceptUePreference");
        return ["error" => "Academic year cannot be empty"];
    }

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in acceptUePreference");
        return ["error" => "Database connection error"];
    }

    // Check if the affectation table exists
    if (!ensureAffectationTable()) {
        error_log("The affectation table does not exist or could not be created");
        mysqli_close($conn);
        return ["error" => "The affectation table does not exist or could not be created"];
    }

    // Ensure the ue_preferences table exists and has the correct structure
    if (!ensureUePreferencesTable()) {
        error_log("Failed to ensure ue_preferences table exists with correct structure");
        mysqli_close($conn);
        return ["error" => "Failed to ensure ue_preferences table exists with correct structure"];
    }

    // Ensure the ue_preferences table has a statut column
    if (!ensureUePreferencesStatusColumn()) {
        error_log("Failed to ensure ue_preferences table has statut column");
        mysqli_close($conn);
        return ["error" => "Failed to ensure ue_preferences table has statut column"];
    }

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Get the preference details
        $preferenceId = mysqli_real_escape_string($conn, $preferenceId);

        // Debug log
        error_log("Fetching preference details for ID: $preferenceId");

        // Check if the table exists
        $tableCheck = mysqli_query($conn, "SHOW TABLES LIKE 'ue_preferences'");
        if (!$tableCheck || mysqli_num_rows($tableCheck) == 0) {
            throw new Exception("Table ue_preferences does not exist");
        }

        // Check table structure
        $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM ue_preferences");
        $columns = [];
        while ($column = mysqli_fetch_assoc($columnsCheck)) {
            $columns[] = $column['Field'];
        }
        error_log("ue_preferences columns: " . implode(", ", $columns));

        // Determine the correct column names based on the table structure
        $enseignantColumn = in_array('id_enseignant', $columns) ? 'id_enseignant' :
                           (in_array('enseignant_id', $columns) ? 'enseignant_id' :
                           (in_array('teacher_id', $columns) ? 'teacher_id' : null));

        $ueColumn = in_array('id_ue', $columns) ? 'id_ue' :
                   (in_array('ue_id', $columns) ? 'ue_id' : null);

        if (!$enseignantColumn || !$ueColumn) {
            throw new Exception("Required columns not found in ue_preferences table. Available columns: " . implode(", ", $columns));
        }

        error_log("Using columns: enseignant=$enseignantColumn, ue=$ueColumn");

        // Use the correct column names
        $query = "SELECT up.*, ue.id as ue_id, ue.module_id, m.filiere_id, f.id_dep
                 FROM ue_preferences up
                 LEFT JOIN uniteenseignement ue ON up.$ueColumn = ue.id
                 LEFT JOIN module m ON ue.module_id = m.id
                 LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                 WHERE up.id = '$preferenceId'";
        $result = mysqli_query($conn, $query);

        if (!$result || mysqli_num_rows($result) == 0) {
            throw new Exception("Preference not found");
        }

        // Check if the preference belongs to the chef's department
        if ($departmentId !== null) {
            $preferenceData = mysqli_fetch_assoc($result);
            $preferenceDepartmentId = $preferenceData['id_dep'];

            error_log("Checking department authorization: chef department=$departmentId, preference department=$preferenceDepartmentId");

            if ($preferenceDepartmentId != $departmentId) {
                throw new Exception("Unauthorized: This preference belongs to another department");
            }

            // Reset the result pointer
            mysqli_data_seek($result, 0);
        }

        $preference = mysqli_fetch_assoc($result);
        $enseignantId = $preference[$enseignantColumn];
        $ueId = $preference[$ueColumn];

        // Update the preference status
        $updateQuery = "UPDATE ue_preferences SET statut = 'acceptee' WHERE id = '$preferenceId'";
        $updateResult = mysqli_query($conn, $updateQuery);

        if (!$updateResult) {
            throw new Exception("Error updating preference status: " . mysqli_error($conn));
        }

        // Check if an affectation already exists
        // Debug log for column names
        error_log("Checking for existing affectation with: professeur_id=$enseignantId, unite_enseignement_id=$ueId, annee_academique=$academicYear");

        // Use the correct column names for the affectation table
        $checkQuery = "SELECT id FROM affectation
                      WHERE professeur_id = '$enseignantId'
                      AND unite_enseignement_id = '$ueId'
                      AND annee_academique = '$academicYear'";
        $checkResult = mysqli_query($conn, $checkQuery);

        if ($checkResult && mysqli_num_rows($checkResult) > 0) {
            // Update existing affectation
            $affectation = mysqli_fetch_assoc($checkResult);
            $affectationId = $affectation['id'];

            // Check if the affectation table has a statut column
            $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
            $affectationColumns = [];
            while ($column = mysqli_fetch_assoc($columnsCheck)) {
                $affectationColumns[] = $column['Field'];
            }
            error_log("affectation columns: " . implode(", ", $affectationColumns));

            // Build the update query based on available columns
            $updateFields = [];

            // Only include statut if it exists in the table
            if (in_array('statut', $affectationColumns)) {
                $updateFields[] = "statut = 'acceptee'";
            }

            // Only include commentaire if it exists in the table
            if (in_array('commentaire', $affectationColumns)) {
                $updateFields[] = "commentaire = " . ($commentaire ? "'$commentaire'" : "NULL");
            }

            // If there are fields to update, build and execute the query
            if (!empty($updateFields)) {
                $updateAffectationQuery = "UPDATE affectation
                                          SET " . implode(", ", $updateFields) . "
                                          WHERE id = '$affectationId'";
                error_log("Update affectation query: " . $updateAffectationQuery);
                $updateAffectationResult = mysqli_query($conn, $updateAffectationQuery);

                if (!$updateAffectationResult) {
                    throw new Exception("Error updating affectation: " . mysqli_error($conn));
                }
            } else {
                error_log("No fields to update in affectation table");
            }
        } else {
            // Create new affectation
            // Check the structure of the affectation table if not already done
            if (!isset($affectationColumns)) {
                $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
                $affectationColumns = [];
                while ($column = mysqli_fetch_assoc($columnsCheck)) {
                    $affectationColumns[] = $column['Field'];
                }
                error_log("affectation columns: " . implode(", ", $affectationColumns));
            }

            // Build the insert query based on available columns
            $columns = [];
            $values = [];

            // Add professeur_id (required)
            $columns[] = "professeur_id";
            $values[] = "'$enseignantId'";

            // Add unite_enseignement_id (required)
            $columns[] = "unite_enseignement_id";
            $values[] = "'$ueId'";

            // Add annee_academique (required)
            $columns[] = "annee_academique";
            $values[] = "'$academicYear'";

            // Add statut if it exists
            if (in_array('statut', $affectationColumns)) {
                $columns[] = "statut";
                $values[] = "'acceptee'";
            }

            // Add commentaire if it exists and is provided
            if (in_array('commentaire', $affectationColumns) && $commentaire) {
                $columns[] = "commentaire";
                $values[] = "'$commentaire'";
            }

            // Build and execute the insert query
            $insertQuery = "INSERT INTO affectation (" . implode(", ", $columns) . ")
                           VALUES (" . implode(", ", $values) . ")";
            error_log("Insert affectation query: " . $insertQuery);
            $insertResult = mysqli_query($conn, $insertQuery);

            if (!$insertResult) {
                throw new Exception("Error creating affectation: " . mysqli_error($conn));
            }
        }

        mysqli_commit($conn);
        mysqli_close($conn);
        return true;
    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in acceptUePreference: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Reject a UE preference
 *
 * @param int $preferenceId The preference ID
 * @param string $commentaire Optional comment
 * @param int $departmentId The department ID of the chef de département (for authorization)
 * @return bool|array True on success, error array on failure
 */
function rejectUePreference($preferenceId, $commentaire = null, $departmentId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in rejectUePreference");
        return ["error" => "Database connection error"];
    }

    // Debug log
    error_log("rejectUePreference called with: preferenceId=$preferenceId, commentaire=" . ($commentaire ?? 'null') . ", departmentId=" . ($departmentId ?? 'null'));

    // Ensure the ue_preferences table exists and has the correct structure
    if (!ensureUePreferencesTable()) {
        error_log("Failed to ensure ue_preferences table exists with correct structure");
        mysqli_close($conn);
        return ["error" => "Failed to ensure ue_preferences table exists with correct structure"];
    }

    // Ensure the ue_preferences table has a statut column
    if (!ensureUePreferencesStatusColumn()) {
        error_log("Failed to ensure ue_preferences table has statut column");
        mysqli_close($conn);
        return ["error" => "Failed to ensure ue_preferences table has statut column"];
    }

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Get the preference details
        $preferenceId = mysqli_real_escape_string($conn, $preferenceId);

        // Debug log
        error_log("Rejecting preference with ID: $preferenceId");

        // Check if the table exists
        $tableCheck = mysqli_query($conn, "SHOW TABLES LIKE 'ue_preferences'");
        if (!$tableCheck || mysqli_num_rows($tableCheck) == 0) {
            throw new Exception("Table ue_preferences does not exist");
        }

        // Check table structure
        $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM ue_preferences");
        $columns = [];
        while ($column = mysqli_fetch_assoc($columnsCheck)) {
            $columns[] = $column['Field'];
        }
        error_log("ue_preferences columns: " . implode(", ", $columns));

        // Check if the preference belongs to the chef's department
        if ($departmentId !== null) {
            $authQuery = "SELECT up.id, f.id_dep
                         FROM ue_preferences up
                         LEFT JOIN uniteenseignement ue ON up.id_ue = ue.id
                         LEFT JOIN module m ON ue.module_id = m.id
                         LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                         WHERE up.id = '$preferenceId'";

            $authResult = mysqli_query($conn, $authQuery);

            if (!$authResult || mysqli_num_rows($authResult) == 0) {
                throw new Exception("Preference not found");
            }

            $preferenceData = mysqli_fetch_assoc($authResult);
            $preferenceDepartmentId = $preferenceData['id_dep'];

            error_log("Checking department authorization: chef department=$departmentId, preference department=$preferenceDepartmentId");

            if ($preferenceDepartmentId != $departmentId) {
                throw new Exception("Unauthorized: This preference belongs to another department");
            }
        }

        // Update the preference status
        $updateQuery = "UPDATE ue_preferences SET statut = 'rejetee' WHERE id = '$preferenceId'";
        $updateResult = mysqli_query($conn, $updateQuery);

        if (!$updateResult) {
            throw new Exception("Error updating preference status: " . mysqli_error($conn));
        }

        mysqli_commit($conn);
        mysqli_close($conn);
        return true;
    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in rejectUePreference: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get the current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
if (!function_exists('getCurrentAcademicYear')) {
    function getCurrentAcademicYear() {
        $currentMonth = date('n'); // 1-12
        $currentYear = date('Y');

        // If we're in the second half of the calendar year (August-December),
        // the academic year is currentYear/currentYear+1
        if ($currentMonth >= 8) {
            return $currentYear . '-' . ($currentYear + 1);
        }
        // If we're in the first half of the calendar year (January-July),
        // the academic year is currentYear-1/currentYear
        else {
            return ($currentYear - 1) . '-' . $currentYear;
        }
    }
}

/**
 * Get all affectations for a specific department
 *
 * @param int $departmentId The department ID
 * @return array Array of affectations for the department
 */
function getAffectationsByDepartment($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAffectationsByDepartment");
        return ["error" => "Database connection error"];
    }

    // Ensure the department ID is safe
    $departmentId = mysqli_real_escape_string($conn, $departmentId);

    // Check if the affectation table has a date_affectation column
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $affectationColumns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $affectationColumns[] = $column['Field'];
    }
    error_log("affectation columns in getAffectationsByDepartment: " . implode(", ", $affectationColumns));

    // Determine which date column to use for formatting and ordering
    $dateColumn = '';
    $dateFormatPart = '';

    if (in_array('date_affectation', $affectationColumns)) {
        $dateColumn = 'date_affectation';
        $dateFormatPart = "DATE_FORMAT(a.date_affectation, '%d/%m/%Y') as date_affectation_formattee";
    } else if (in_array('created_at', $affectationColumns)) {
        $dateColumn = 'created_at';
        $dateFormatPart = "DATE_FORMAT(a.created_at, '%d/%m/%Y') as date_affectation_formattee";
    } else {
        // If neither column exists, don't include date formatting in the query
        $dateFormatPart = "a.annee_academique as date_affectation_formattee";
    }

    $sql = "SELECT a.id, a.professeur_id, a.unite_enseignement_id, a.annee_academique, " .
           (in_array('statut', $affectationColumns) ? "a.statut, " : "") .
           (in_array('commentaire', $affectationColumns) ? "a.commentaire, " : "") .
           "ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
            e.nom as enseignant_nom, e.prenom as enseignant_prenom, e.id_enseignant, e.CNI as enseignant_cni,
            $dateFormatPart
            FROM affectation a
            LEFT JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN enseignant e ON a.professeur_id = e.id_enseignant
            WHERE f.id_dep = '$departmentId'
            ORDER BY " . ($dateColumn ? "a.$dateColumn" : "a.annee_academique") . " DESC, m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAffectationsByDepartment: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching affectations: " . $error];
    }

    $affectations = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $affectations[] = $row;
    }

    mysqli_close($conn);
    return $affectations;
}

/**
 * Create a manual affectation
 *
 * @param int $teacherId The teacher ID
 * @param int $ueId The teaching unit ID
 * @param string $academicYear The academic year
 * @param string|null $commentaire Optional comment
 * @return bool|array True on success, error array on failure
 */
function createManualAffectation($teacherId, $ueId, $academicYear, $commentaire = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createManualAffectation");
        return ["error" => "Database connection error"];
    }

    // Validate parameters
    if (empty($teacherId)) {
        error_log("Empty teacher ID provided to createManualAffectation");
        return ["error" => "Teacher ID cannot be empty"];
    }

    if (empty($ueId)) {
        error_log("Empty UE ID provided to createManualAffectation");
        return ["error" => "UE ID cannot be empty"];
    }

    if (empty($academicYear)) {
        error_log("Empty academic year provided to createManualAffectation");
        return ["error" => "Academic year cannot be empty"];
    }

    // Check if the affectation table exists
    if (!ensureAffectationTable()) {
        error_log("The affectation table does not exist or could not be created");
        mysqli_close($conn);
        return ["error" => "The affectation table does not exist or could not be created"];
    }

    // Sanitize inputs
    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $ueId = mysqli_real_escape_string($conn, $ueId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    $commentaire = $commentaire ? mysqli_real_escape_string($conn, $commentaire) : null;

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Check if the teacher exists
        $teacherQuery = "SELECT id_enseignant FROM enseignant WHERE id_enseignant = '$teacherId'";
        $teacherResult = mysqli_query($conn, $teacherQuery);

        if (!$teacherResult || mysqli_num_rows($teacherResult) == 0) {
            throw new Exception("Teacher not found");
        }

        // Check if the UE exists
        $ueQuery = "SELECT id FROM uniteenseignement WHERE id = '$ueId'";
        $ueResult = mysqli_query($conn, $ueQuery);

        if (!$ueResult || mysqli_num_rows($ueResult) == 0) {
            throw new Exception("Teaching unit not found");
        }

        // Check if an affectation already exists
        $checkQuery = "SELECT id FROM affectation
                      WHERE professeur_id = '$teacherId'
                      AND unite_enseignement_id = '$ueId'
                      AND annee_academique = '$academicYear'";
        $checkResult = mysqli_query($conn, $checkQuery);

        if ($checkResult && mysqli_num_rows($checkResult) > 0) {
            // Update existing affectation
            $affectation = mysqli_fetch_assoc($checkResult);
            $affectationId = $affectation['id'];

            // Check if the affectation table has a statut column
            $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
            $affectationColumns = [];
            while ($column = mysqli_fetch_assoc($columnsCheck)) {
                $affectationColumns[] = $column['Field'];
            }
            error_log("affectation columns in createManualAffectation: " . implode(", ", $affectationColumns));

            // Build the update query based on available columns
            $updateFields = [];

            // Only include statut if it exists in the table
            if (in_array('statut', $affectationColumns)) {
                $updateFields[] = "statut = 'acceptee'";
            }

            // Only include commentaire if it exists in the table
            if (in_array('commentaire', $affectationColumns)) {
                $updateFields[] = "commentaire = " . ($commentaire ? "'$commentaire'" : "NULL");
            }

            // If there are fields to update, build and execute the query
            if (!empty($updateFields)) {
                $updateAffectationQuery = "UPDATE affectation
                                          SET " . implode(", ", $updateFields) . "
                                          WHERE id = '$affectationId'";
                error_log("Update affectation query: " . $updateAffectationQuery);
                $updateAffectationResult = mysqli_query($conn, $updateAffectationQuery);

                if (!$updateAffectationResult) {
                    throw new Exception("Error updating affectation: " . mysqli_error($conn));
                }
            } else {
                error_log("No fields to update in affectation table");
            }
        } else {
            // Create new affectation
            // Check the structure of the affectation table if not already done
            if (!isset($affectationColumns)) {
                $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
                $affectationColumns = [];
                while ($column = mysqli_fetch_assoc($columnsCheck)) {
                    $affectationColumns[] = $column['Field'];
                }
                error_log("affectation columns in createManualAffectation: " . implode(", ", $affectationColumns));
            }

            // Build the insert query based on available columns
            $columns = [];
            $values = [];

            // Add professeur_id (required)
            $columns[] = "professeur_id";
            $values[] = "'$teacherId'";

            // Add unite_enseignement_id (required)
            $columns[] = "unite_enseignement_id";
            $values[] = "'$ueId'";

            // Add annee_academique (required)
            $columns[] = "annee_academique";
            $values[] = "'$academicYear'";

            // Add statut if it exists
            if (in_array('statut', $affectationColumns)) {
                $columns[] = "statut";
                $values[] = "'acceptee'";
            }

            // Add commentaire if it exists and is provided
            if (in_array('commentaire', $affectationColumns) && $commentaire) {
                $columns[] = "commentaire";
                $values[] = "'$commentaire'";
            }

            // Build and execute the insert query
            $insertQuery = "INSERT INTO affectation (" . implode(", ", $columns) . ")
                           VALUES (" . implode(", ", $values) . ")";
            error_log("Insert affectation query: " . $insertQuery);
            $insertResult = mysqli_query($conn, $insertQuery);

            if (!$insertResult) {
                throw new Exception("Error creating affectation: " . mysqli_error($conn));
            }
        }

        mysqli_commit($conn);
        mysqli_close($conn);
        return true;
    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in createManualAffectation: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get modules by department ID
 *
 * @param int $departmentId The department ID
 * @return array Array of modules for the department
 */
function getModulesByDepartment($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesByDepartment");
        return ["error" => "Database connection error"];
    }

    // Ensure the department ID is safe
    $departmentId = mysqli_real_escape_string($conn, $departmentId);

    $sql = "SELECT m.*, f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            WHERE f.id_dep = '$departmentId'
            ORDER BY m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesByDepartment: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}
/**
 * Get all affectations for a specific filiere
 *
 * @param int $filiereId The filiere ID
 * @return array Array of affectations for the filiere
 */
function getAffectationsByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAffectationsByFiliere");
        return ["error" => "Database connection error"];
    }

    // Ensure the filiere ID is safe
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Check if the affectation table has a date_affectation column
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $affectationColumns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $affectationColumns[] = $column['Field'];
    }
    error_log("affectation columns in getAffectationsByFiliere: " . implode(", ", $affectationColumns));

    // Determine which date column to use for formatting and ordering
    $dateColumn = '';
    $dateFormatPart = '';

    if (in_array('date_affectation', $affectationColumns)) {
        $dateColumn = 'date_affectation';
        $dateFormatPart = "DATE_FORMAT(a.date_affectation, '%d/%m/%Y') as date_affectation_formattee";
    } else if (in_array('created_at', $affectationColumns)) {
        $dateColumn = 'created_at';
        $dateFormatPart = "DATE_FORMAT(a.created_at, '%d/%m/%Y') as date_affectation_formattee";
    } else {
        // If neither column exists, don't include date formatting in the query
        $dateFormatPart = "a.annee_academique as date_affectation_formattee";
    }

    $sql = "SELECT a.id, a.professeur_id, a.unite_enseignement_id, a.annee_academique, " .
           (in_array('statut', $affectationColumns) ? "a.statut, " : "") .
           (in_array('commentaire', $affectationColumns) ? "a.commentaire, " : "") .
           "ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre, sem.id as semestre_id,
            e.nom as enseignant_nom, e.prenom as enseignant_prenom, e.id_enseignant, e.CNI as enseignant_cni,
            $dateFormatPart
            FROM affectation a
            LEFT JOIN uniteenseignement ue ON a.unite_enseignement_id = ue.id
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN enseignant e ON a.professeur_id = e.id_enseignant
            WHERE f.id_filiere = '$filiereId'
            ORDER BY sem.id, m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAffectationsByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching affectations: " . $error];
    }

    $affectations = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $affectations[] = $row;
    }

    mysqli_close($conn);
    return $affectations;
}
?>
