<?php
// Démarrer la session si elle n'est pas déjà active
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Inclure l'utilitaire de chemin
require_once __DIR__ . '/utils/path_utils.php';

// Vérifier si l'utilisateur est connecté et est un administrateur
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Non autorisé']);
    exit();
}

// Connexion à la base de données
function getConnection() {
    $host = 'localhost';
    $dbname = 'ensah';
    $username = 'root';
    $password = '';

    $conn = mysqli_connect($host, $username, $password, $dbname);

    if (!$conn) {
        die("Connexion échouée: " . mysqli_connect_error());
    }

    mysqli_set_charset($conn, "utf8");
    return $conn;
}

// Fonction pour mettre à jour la photo de profil
function updatePhoto() {
    // Vérifier si un CNI a été fourni
    if (!isset($_GET['cni']) || empty($_GET['cni'])) {
        echo json_encode(['success' => false, 'error' => 'CNI manquant']);
        exit();
    }

    $cni = $_GET['cni'];

    // Récupérer l'ancienne photo pour la supprimer plus tard
    $conn = getConnection();
    $cniEscaped = mysqli_real_escape_string($conn, $cni);
    $query = "SELECT photo_url FROM admin WHERE CNI = '$cniEscaped'";
    $result = mysqli_query($conn, $query);
    $oldPhotoUrl = '';

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $oldPhotoUrl = $row['photo_url'];
        error_log("Ancienne photo trouvée: " . $oldPhotoUrl);
    }
    mysqli_close($conn);

    // Vérifier si un fichier a été uploadé
    if (!isset($_FILES['profileImage']) || $_FILES['profileImage']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'error' => 'Aucune image n\'a été envoyée ou erreur lors de l\'upload']);
        exit();
    }

    // Créer le dossier d'upload s'il n'existe pas
    // Utiliser le chemin du document root et le chemin de base du projet
    $basePath = getBasePath();
    $relativePath = 'view/assets/img/profile/';
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $relativePath;

    // Vérifier si le dossier existe
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Vérifier les permissions du dossier
    if (!is_writable($uploadDir)) {
        chmod($uploadDir, 0777);
    }

    // Vérifier le type de fichier
    $imageFile = $_FILES['profileImage'];
    $imageFileType = strtolower(pathinfo($imageFile['name'], PATHINFO_EXTENSION));

    if (!in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
        echo json_encode(['success' => false, 'error' => 'Seuls les fichiers JPG, JPEG, PNG & GIF sont autorisés']);
        exit();
    }

    // Générer un nom de fichier unique
    $newFileName = $cni . '_' . time() . '.' . $imageFileType;
    $targetFile = $uploadDir . $newFileName;

    // Vérifier si le fichier temporaire existe
    if (!file_exists($imageFile['tmp_name'])) {
        echo json_encode(['success' => false, 'error' => 'Le fichier temporaire n\'existe pas']);
        exit();
    }

    // Uploader le fichier
    $uploadResult = move_uploaded_file($imageFile['tmp_name'], $targetFile);

    if (!$uploadResult) {
        echo json_encode(['success' => false, 'error' => 'Erreur lors de l\'upload de l\'image']);
        exit();
    }

    // Supprimer l'ancienne photo si elle existe
    if (!empty($oldPhotoUrl)) {
        // Extraire le nom du fichier de l'URL relative
        $oldFileName = '';

        // Si c'est un chemin relatif complet (ex: assets/img/profile/image.jpg)
        if (strpos($oldPhotoUrl, '/') !== false) {
            $oldFileName = basename($oldPhotoUrl);
        } else {
            // Si c'est juste un nom de fichier
            $oldFileName = $oldPhotoUrl;
        }

        // Définir tous les chemins possibles où l'ancienne photo pourrait se trouver
        $possiblePaths = [
            // Chemin basé sur le document root et le chemin de base du projet
            $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $relativePath . $oldFileName,
            // Chemin basé sur le répertoire courant
            __DIR__ . '/' . $relativePath . $oldFileName,
            // Chemin basé sur le chemin relatif complet
            $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $oldPhotoUrl
        ];

        // Essayer de supprimer l'ancienne photo avec tous les chemins possibles
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                try {
                    if (unlink($path)) {
                        break;
                    }
                } catch (Exception $e) {
                    // Ignorer les erreurs de suppression
                }
            }
        }
    }

    // Mettre à jour la base de données
    $conn = getConnection();
    // Stocker une URL relative dans la base de données
    // Format: view/assets/img/profile/filename.jpg
    $photoUrl = $relativePath . $newFileName;
    $photoUrl = mysqli_real_escape_string($conn, $photoUrl);
    $cni = mysqli_real_escape_string($conn, $cni);

    $query = "UPDATE admin SET photo_url = '$photoUrl' WHERE CNI = '$cni'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        echo json_encode(['success' => false, 'error' => 'Erreur lors de la mise à jour de la photo dans la base de données: ' . mysqli_error($conn)]);
        exit();
    }

    // Vérifier si des lignes ont été affectées
    $affectedRows = mysqli_affected_rows($conn);

    $response = [
        'success' => true,
        'photo_url' => $photoUrl,
        'message' => 'Photo de profil mise à jour avec succès',
        'affected_rows' => $affectedRows
    ];
    echo json_encode($response);

    mysqli_close($conn);
}

// Fonction pour supprimer la photo de profil
function deletePhoto() {
    global $basePath;

    // Vérifier si un CNI a été fourni
    if (!isset($_GET['cni']) || empty($_GET['cni'])) {
        echo json_encode(['success' => false, 'error' => 'CNI manquant']);
        exit();
    }

    $cni = $_GET['cni'];
    $basePath = getBasePath();
    $relativePath = 'view/assets/img/profile/';

    // Récupérer l'URL de la photo actuelle
    $conn = getConnection();
    $cni = mysqli_real_escape_string($conn, $cni);

    $query = "SELECT photo_url FROM admin WHERE CNI = '$cni'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $photoUrl = $row['photo_url'];

        // Supprimer le fichier physique si possible
        if (!empty($photoUrl)) {
            // Extraire le nom du fichier de l'URL relative
            $fileName = '';

            // Si c'est un chemin relatif complet (ex: assets/img/profile/image.jpg)
            if (strpos($photoUrl, '/') !== false) {
                $fileName = basename($photoUrl);
            } else {
                // Si c'est juste un nom de fichier
                $fileName = $photoUrl;
            }

            // Définir tous les chemins possibles où la photo pourrait se trouver
            $possiblePaths = [
                // Chemin basé sur le document root et le chemin de base du projet
                $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $relativePath . $fileName,
                // Chemin basé sur le répertoire courant
                __DIR__ . '/' . $relativePath . $fileName,
                // Chemin basé sur le chemin relatif complet
                $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $photoUrl
            ];

            // Essayer de supprimer la photo avec tous les chemins possibles
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    try {
                        if (unlink($path)) {
                            break;
                        }
                    } catch (Exception $e) {
                        // Ignorer les erreurs de suppression
                    }
                }
            }
        }

        // Mettre à jour la base de données
        $query = "UPDATE admin SET photo_url = '' WHERE CNI = '$cni'";
        $result = mysqli_query($conn, $query);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Photo de profil supprimée avec succès'
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Erreur lors de la suppression de la photo dans la base de données: ' . mysqli_error($conn)]);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Administrateur non trouvé']);
    }

    mysqli_close($conn);
}

// Traiter la requête
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'updatePhoto') {
    updatePhoto();
} elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE' || (isset($_GET['action']) && $_GET['action'] === 'deletePhoto')) {
    deletePhoto();
} else {
    echo json_encode(['success' => false, 'error' => 'Méthode non autorisée ou action manquante']);
}
?>
