<?php
require_once "../model/enseignantModel.php";
require_once "../model/niveauModel.php";
require_once "../model/semestreModel.php";
require_once "../model/moduleModel.php";
require_once "../model/noteModel.php";
require_once "../utils/response.php";

/**
 * Get teachers associated with a coordinator's field
 *
 * @return array Array of teachers
 */
function getTeachersByCoordinatorFieldAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;

    if (!$filiereId) {
        jsonResponse(['error' => 'Coordinator field not found'], 400);
        exit;
    }

    // Get the database connection
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize input
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Query to get teachers associated with the coordinator's field
    $query = "SELECT e.id_enseignant, e.CNI, e.nom, e.prenom
              FROM enseignant e
              JOIN enseignant_filiere ef ON e.id_enseignant = ef.id_enseignant
              WHERE ef.id_filiere = '$filiereId'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeachersByCoordinatorFieldAPI: " . $error);
        mysqli_close($conn);
        jsonResponse(['error' => 'Error fetching teachers: ' . $error], 500);
        exit;
    }

    $teachers = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $teachers[] = $row;
    }

    mysqli_close($conn);
    jsonResponse(['data' => $teachers], 200);
}

/**
 * Get levels for a teacher and field
 *
 * @param int $teacherId The teacher ID
 * @return array Array of levels
 */
function getLevelsByTeacherAndFieldAPI($teacherId) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;

    if (!$filiereId || !$teacherId) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    // Get the database connection
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize inputs
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Query to get levels for the teacher and field
    $query = "SELECT DISTINCT n.id as id_niveau, n.nom as niveau
              FROM niveaux n
              JOIN module m ON n.id = m.id_niveau
              JOIN uniteenseignement ue ON m.id = ue.module_id
              JOIN affectation a ON ue.id = a.unite_enseignement_id
              WHERE a.professeur_id = '$teacherId'
              AND m.filiere_id = '$filiereId'
              ORDER BY n.nom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getLevelsByTeacherAndFieldAPI: " . $error);
        mysqli_close($conn);
        jsonResponse(['error' => 'Error fetching levels: ' . $error], 500);
        exit;
    }

    $levels = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $levels[] = $row;
    }

    mysqli_close($conn);
    jsonResponse(['data' => $levels], 200);
}

/**
 * Get semesters for a level
 *
 * @param int $levelId The level ID
 * @return array Array of semesters
 */
function getSemestersByLevelAPI($levelId) {
    if (!$levelId) {
        jsonResponse(['error' => 'Level ID is required'], 400);
        exit;
    }

    $semesters = getSemestresByNiveau($levelId);

    if (isset($semesters['error'])) {
        jsonResponse(['error' => $semesters['error']], 500);
        exit;
    }

    jsonResponse(['data' => $semesters], 200);
}

/**
 * Get modules for a teacher, level, semester, and field
 *
 * @param int $teacherId The teacher ID
 * @param int $levelId The level ID
 * @param string $semester The semester
 * @return array Array of modules
 */
function getModulesByFiltersAPI($teacherId, $levelId, $semester) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator or teacher
    if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] !== 'coordinateur' && $_SESSION['user']['role'] !== 'enseignant')) {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get the field ID based on user role
    $filiereId = null;

    if ($_SESSION['user']['role'] === 'coordinateur') {
        // For coordinators, get field ID from session
        $filiereId = $_SESSION['user']['filiere_id'] ?? null;
    } else if ($_SESSION['user']['role'] === 'enseignant') {
        // For teachers, get field ID from the request parameters or try to determine from the database
        if (isset($_GET['filiere'])) {
            $filiereId = $_GET['filiere'];
        }
    }

    if (!$filiereId || !$teacherId || !$levelId || !$semester) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    // Get the database connection
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize inputs
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $levelId = mysqli_real_escape_string($conn, $levelId);
    $semester = mysqli_real_escape_string($conn, $semester);

    // We'll determine the semester field name after getting all columns

    // Get detailed information about the module table structure
    $moduleColumnsQuery = mysqli_query($conn, "SHOW COLUMNS FROM module");
    $moduleColumns = [];
    while ($col = mysqli_fetch_assoc($moduleColumnsQuery)) {
        $moduleColumns[] = $col['Field'];
    }
    error_log("Module table columns: " . implode(", ", $moduleColumns));

    // Get sample values from the semestre field to understand its format
    // First check if the semestre field exists and use the correct field name
    if (in_array('semestre', $moduleColumns)) {
        $sampleSemestresQuery = mysqli_query($conn, "SELECT DISTINCT semestre FROM module LIMIT 5");
        if ($sampleSemestresQuery) {
            $sampleSemestres = [];
            while ($row = mysqli_fetch_assoc($sampleSemestresQuery)) {
                $sampleSemestres[] = $row['semestre'];
            }
            error_log("Sample semestre values in module table: " . implode(", ", $sampleSemestres));
        }

        // Get a sample of modules with their semesters
        $sampleModulesQuery = mysqli_query($conn, "SELECT id, nom, semestre FROM module LIMIT 10");
        if ($sampleModulesQuery) {
            error_log("Sample modules with their semesters:");
            while ($row = mysqli_fetch_assoc($sampleModulesQuery)) {
                error_log("Module ID: " . $row['id'] . ", Name: " . $row['nom'] . ", Semester: " . $row['semestre']);
            }
        }
    } elseif (in_array('id_semestre', $moduleColumns)) {
        $sampleSemestresQuery = mysqli_query($conn, "SELECT DISTINCT id_semestre FROM module LIMIT 5");
        if ($sampleSemestresQuery) {
            $sampleSemestres = [];
            while ($row = mysqli_fetch_assoc($sampleSemestresQuery)) {
                $sampleSemestres[] = $row['id_semestre'];
            }
            error_log("Sample id_semestre values in module table: " . implode(", ", $sampleSemestres));
        }

        // Get a sample of modules with their semesters
        $sampleModulesQuery = mysqli_query($conn, "SELECT id, nom, id_semestre FROM module LIMIT 10");
        if ($sampleModulesQuery) {
            error_log("Sample modules with their semesters:");
            while ($row = mysqli_fetch_assoc($sampleModulesQuery)) {
                error_log("Module ID: " . $row['id'] . ", Name: " . $row['nom'] . ", Semester: " . $row['id_semestre']);
            }
        }
    } else {
        error_log("Neither 'semestre' nor 'id_semestre' column found in module table");
    }

    // Check specifically for the module "Programmation C++" and its semester
    $cppModuleQuery = mysqli_query($conn, "SELECT * FROM module WHERE nom LIKE '%Programmation C++%'");
    if ($cppModuleQuery && mysqli_num_rows($cppModuleQuery) > 0) {
        error_log("Found Programmation C++ module(s):");
        while ($row = mysqli_fetch_assoc($cppModuleQuery)) {
            $semValue = isset($row['semestre']) ? $row['semestre'] : (isset($row['id_semestre']) ? $row['id_semestre'] : 'unknown');
            error_log("Module ID: " . $row['id'] . ", Name: " . $row['nom'] . ", Semester: " . $semValue);
        }
    } else {
        error_log("No Programmation C++ module found or error in query");
    }

    // Check if there are multiple entries for the same module with different semesters
    $duplicateCheckQuery = "SELECT nom, COUNT(DISTINCT id_semestre) as semester_count
                           FROM module
                           WHERE nom LIKE '%Programmation C++%'
                           GROUP BY nom
                           HAVING COUNT(DISTINCT id_semestre) > 1";
    $duplicateCheckResult = mysqli_query($conn, $duplicateCheckQuery);
    if ($duplicateCheckResult && mysqli_num_rows($duplicateCheckResult) > 0) {
        error_log("WARNING: Found modules with the same name but different semesters:");
        while ($row = mysqli_fetch_assoc($duplicateCheckResult)) {
            error_log("Module: " . $row['nom'] . " has " . $row['semester_count'] . " different semesters");
        }
    } else {
        error_log("No modules found with the same name but different semesters");
    }

    // Check if there are multiple entries in uniteenseignement for the same module
    $ueCheckQuery = "SELECT m.nom, m.id, COUNT(ue.id) as ue_count
                    FROM module m
                    JOIN uniteenseignement ue ON m.id = ue.module_id
                    WHERE m.nom LIKE '%Programmation C++%'
                    GROUP BY m.nom, m.id";
    $ueCheckResult = mysqli_query($conn, $ueCheckQuery);
    if ($ueCheckResult && mysqli_num_rows($ueCheckResult) > 0) {
        error_log("Checking uniteenseignement entries for Programmation C++:");
        while ($row = mysqli_fetch_assoc($ueCheckResult)) {
            error_log("Module: " . $row['nom'] . " (ID: " . $row['id'] . ") has " . $row['ue_count'] . " entries in uniteenseignement");
        }
    } else {
        error_log("No uniteenseignement entries found for Programmation C++");
    }

    // Check if the module table has 'id' or 'id_module' as the primary key
    $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
    error_log("Using module ID field: " . $moduleIdField);

    // Check if the semester field exists and what it's called
    $semestreField = null;

    // First check for exact matches
    if (in_array('id_semestre', $moduleColumns)) {
        $semestreField = 'id_semestre';
    } elseif (in_array('semestre', $moduleColumns)) {
        $semestreField = 'semestre';
    }

    // If not found, try to find any field that might contain semester information
    if (!$semestreField) {
        error_log("WARNING: Could not find standard semester field in module table!");
        foreach ($moduleColumns as $col) {
            if (stripos($col, 'sem') !== false) {
                $semestreField = $col;
                error_log("Found potential semester field: " . $semestreField);
                break;
            }
        }
    }

    // If still not found, we'll need to modify our queries to not use the semester filter
    if (!$semestreField) {
        error_log("WARNING: No semester field found in module table. Semester filtering will be disabled.");
    } else {
        error_log("Using semester field: " . $semestreField);
    }

    // Check if the niveau field exists and what it's called
    $niveauField = in_array('id_niveau', $moduleColumns) ? 'id_niveau' :
                  (in_array('niveau_id', $moduleColumns) ? 'niveau_id' : null);

    if (!$niveauField) {
        error_log("WARNING: Could not find niveau field in module table!");
        // Try to find any field that might contain niveau information
        foreach ($moduleColumns as $col) {
            if (stripos($col, 'niv') !== false) {
                $niveauField = $col;
                error_log("Found potential niveau field: " . $niveauField);
                break;
            }
        }

        // If still not found, use a default
        if (!$niveauField) {
            $niveauField = 'id_niveau';
            error_log("Using default niveau field name: " . $niveauField);
        }
    }

    // Check if the filiere field exists and what it's called
    $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' :
                   (in_array('id_filiere', $moduleColumns) ? 'id_filiere' : null);

    if (!$filiereField) {
        error_log("WARNING: Could not find filiere field in module table!");
        // Try to find any field that might contain filiere information
        foreach ($moduleColumns as $col) {
            if (stripos($col, 'fil') !== false) {
                $filiereField = $col;
                error_log("Found potential filiere field: " . $filiereField);
                break;
            }
        }

        // If still not found, use a default
        if (!$filiereField) {
            $filiereField = 'filiere_id';
            error_log("Using default filiere field name: " . $filiereField);
        }
    }

    // Log the values we're searching for
    error_log("Searching for modules with: teacherId=$teacherId, filiereId=$filiereId, levelId=$levelId, semester=$semester");
    error_log("Using fields: moduleId=$moduleIdField, filiere=$filiereField, niveau=$niveauField, semestre=$semestreField");

    // Query to get modules using the specific join between affectation, uniteenseignement, and module
    // Add the semester condition directly in the main query to ensure it's properly applied
    if ($semestreField && !empty($semester)) {
        $query = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                  FROM module m
                  JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                  JOIN affectation a ON ue.id = a.unite_enseignement_id
                  WHERE a.professeur_id = '$teacherId'
                  AND m.$filiereField = '$filiereId'
                  AND m.$niveauField = '$levelId'
                  AND m.$semestreField = '$semester'";

        error_log("Using query with semester filter: m.$semestreField = '$semester'");
    } else {
        $query = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                  FROM module m
                  JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                  JOIN affectation a ON ue.id = a.unite_enseignement_id
                  WHERE a.professeur_id = '$teacherId'
                  AND m.$filiereField = '$filiereId'
                  AND m.$niveauField = '$levelId'";

        error_log("Using query without semester filter");
    }

    // Log the final query
    error_log("Final query: " . $query);

    // Double-check if there are any modules with this semester ID
    if ($semestreField && !empty($semester)) {
        $semesterCheckQuery = "SELECT COUNT(*) as count FROM module m WHERE m.$semestreField = '$semester'";
        $semesterCheckResult = mysqli_query($conn, $semesterCheckQuery);
        if ($semesterCheckResult) {
            $row = mysqli_fetch_assoc($semesterCheckResult);
            error_log("Number of modules with semester ID $semester: " . $row['count']);
        }
    }

    $query .= " ORDER BY m.nom";

    error_log("Executing query: " . $query);

    // Test query to see if any modules match our criteria
    $testQuery = "SELECT COUNT(*) as count FROM ($query) as subquery";
    $testResult = mysqli_query($conn, $testQuery);
    if ($testResult) {
        $row = mysqli_fetch_assoc($testResult);
        error_log("Number of modules matching the criteria: " . $row['count']);
    }

    // Check if the Programmation C++ module is included in the results
    $cppCheckQuery = str_replace("SELECT DISTINCT m.$moduleIdField as id, m.nom", "SELECT DISTINCT m.$moduleIdField as id, m.nom, m.$semestreField as semester_value", $query);
    $cppCheckQuery .= " AND m.nom LIKE '%Programmation C++%'";
    error_log("Checking if C++ module matches criteria: " . $cppCheckQuery);

    $cppCheckResult = mysqli_query($conn, $cppCheckQuery);
    if ($cppCheckResult) {
        if (mysqli_num_rows($cppCheckResult) > 0) {
            $cppModule = mysqli_fetch_assoc($cppCheckResult);
            error_log("C++ module IS included in results. ID: " . $cppModule['id'] . ", Semester value: " . $cppModule['semester_value']);
        } else {
            error_log("C++ module is NOT included in results with the current semester filter");
        }
    } else {
        error_log("Error checking C++ module: " . mysqli_error($conn));
    }

    // Check the actual join between module, uniteenseignement, and affectation
    $joinCheckQuery = "SELECT m.$moduleIdField as module_id, m.nom as module_name, m.$semestreField as semester_id,
                      ue.id as ue_id, ue.module_id as ue_module_id,
                      a.id as affectation_id, a.unite_enseignement_id, a.professeur_id
                      FROM module m
                      JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                      JOIN affectation a ON ue.id = a.unite_enseignement_id
                      WHERE m.nom LIKE '%Programmation C++%'
                      AND a.professeur_id = '$teacherId'";

    error_log("Checking join relationships: " . $joinCheckQuery);
    $joinCheckResult = mysqli_query($conn, $joinCheckQuery);

    if ($joinCheckResult && mysqli_num_rows($joinCheckResult) > 0) {
        error_log("Found " . mysqli_num_rows($joinCheckResult) . " join relationships for C++ module:");
        while ($row = mysqli_fetch_assoc($joinCheckResult)) {
            error_log("Module ID: " . $row['module_id'] .
                     ", Module Name: " . $row['module_name'] .
                     ", Semester ID: " . $row['semester_id'] .
                     ", UE ID: " . $row['ue_id'] .
                     ", UE Module ID: " . $row['ue_module_id'] .
                     ", Affectation ID: " . $row['affectation_id'] .
                     ", UE ID in Affectation: " . $row['unite_enseignement_id'] .
                     ", Professor ID: " . $row['professeur_id']);
        }
    } else {
        error_log("No join relationships found for C++ module or error in query");
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesByFiltersAPI with direct query: " . $error);

        // Try a more permissive query with a more flexible semester condition
        error_log("Trying more permissive query with flexible semester condition");

        // Permissive query with or without semester condition
        if ($semestreField && !empty($semester)) {
            $permissiveQuery = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                               FROM module m
                               JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                               JOIN affectation a ON ue.id = a.unite_enseignement_id
                               WHERE a.professeur_id = '$teacherId'
                               AND m.$filiereField = '$filiereId'
                               AND m.$niveauField = '$levelId'
                               AND m.$semestreField = '$semester'";

            error_log("Permissive query with semester filter: m.$semestreField = '$semester'");
        } else {
            $permissiveQuery = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                               FROM module m
                               JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                               JOIN affectation a ON ue.id = a.unite_enseignement_id
                               WHERE a.professeur_id = '$teacherId'
                               AND m.$filiereField = '$filiereId'
                               AND m.$niveauField = '$levelId'";

            error_log("Permissive query without semester filter");
        }

        $permissiveQuery .= " ORDER BY m.nom";

        error_log("Executing permissive query: " . $permissiveQuery);
        $permissiveResult = mysqli_query($conn, $permissiveQuery);

        if (!$permissiveResult) {
            $permissiveError = mysqli_error($conn);
            error_log("Error in permissive query: " . $permissiveError);

            // Last resort: get all modules for this filiere and teacher
            error_log("Trying last resort query - all modules for filiere and teacher");
            $lastResortQuery = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                               FROM module m
                               JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                               JOIN affectation a ON ue.id = a.unite_enseignement_id
                               WHERE a.professeur_id = '$teacherId'
                               AND m.$filiereField = '$filiereId'
                               ORDER BY m.nom";

            error_log("Executing last resort query: " . $lastResortQuery);
            $lastResortResult = mysqli_query($conn, $lastResortQuery);

            if (!$lastResortResult) {
                $lastResortError = mysqli_error($conn);
                error_log("Error in last resort query: " . $lastResortError);
                mysqli_close($conn);
                jsonResponse(['error' => 'Error fetching modules: ' . $lastResortError], 500);
                exit;
            }

            $modules = [];
            while ($row = mysqli_fetch_assoc($lastResortResult)) {
                $modules[] = $row;
            }

            error_log("Last resort query returned " . count($modules) . " modules");
        } else {
            $modules = [];
            while ($row = mysqli_fetch_assoc($permissiveResult)) {
                $modules[] = $row;
            }

            error_log("Permissive query returned " . count($modules) . " modules");
        }
    } else {
        $modules = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $modules[] = $row;
        }

        error_log("Main query returned " . count($modules) . " modules");

        // If no modules found, try a more permissive approach
        if (empty($modules)) {
            error_log("No modules found with main query, trying fallback");

            // Try to get modules with or without semester filter
            if ($semestreField && !empty($semester)) {
                $fallbackQuery = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                                 FROM module m
                                 JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                                 JOIN affectation a ON ue.id = a.unite_enseignement_id
                                 WHERE a.professeur_id = '$teacherId'
                                 AND m.$filiereField = '$filiereId'
                                 AND m.$niveauField = '$levelId'
                                 AND m.$semestreField = '$semester'
                                 ORDER BY m.nom";

                error_log("Fallback query with semester filter: m.$semestreField = '$semester'");
            } else {
                $fallbackQuery = "SELECT DISTINCT m.$moduleIdField as id, m.nom
                                 FROM module m
                                 JOIN uniteenseignement ue ON m.$moduleIdField = ue.module_id
                                 JOIN affectation a ON ue.id = a.unite_enseignement_id
                                 WHERE a.professeur_id = '$teacherId'
                                 AND m.$filiereField = '$filiereId'
                                 AND m.$niveauField = '$levelId'
                                 ORDER BY m.nom";

                error_log("Fallback query without semester filter");
            }

            error_log("Executing fallback query: " . $fallbackQuery);
            $fallbackResult = mysqli_query($conn, $fallbackQuery);

            if ($fallbackResult) {
                while ($row = mysqli_fetch_assoc($fallbackResult)) {
                    $modules[] = $row;
                }

                error_log("Fallback query returned " . count($modules) . " modules");
            }
        }
    }

    mysqli_close($conn);
    jsonResponse(['data' => $modules], 200);
}

/**
 * Handle file upload for grade import
 */
function importGradesFileAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Check if file was uploaded
    if (!isset($_FILES['gradeFile']) || $_FILES['gradeFile']['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['error' => 'No file uploaded or upload error'], 400);
        exit;
    }

    // Check if required parameters are provided
    if (!isset($_POST['teacherId']) || !isset($_POST['moduleId']) || !isset($_POST['levelId']) ||
        !isset($_POST['semester']) || !isset($_POST['session'])) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    // Get the coordinator's field ID from the session
    $filiereId = $_SESSION['user']['filiere_id'] ?? null;
    if (!$filiereId) {
        jsonResponse(['error' => 'Coordinator field not found'], 400);
        exit;
    }

    // Get parameters
    $teacherId = $_POST['teacherId'];
    $moduleId = $_POST['moduleId'];
    $levelId = $_POST['levelId'];
    $semester = $_POST['semester'];
    $session = $_POST['session'];

    // Create upload directory if it doesn't exist
    $uploadDir = "../uploads/grades/";
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate a unique filename
    $filename = "grades_" . $moduleId . "_" . $levelId . "_" . $semester . "_" . $session . "_" . time() . ".pdf";
    $filePath = $uploadDir . $filename;

    // Move the uploaded file
    if (!move_uploaded_file($_FILES['gradeFile']['tmp_name'], $filePath)) {
        jsonResponse(['error' => 'Failed to save the file'], 500);
        exit;
    }

    // Create a record in the database
    $conn = getConnection();
    if (!$conn) {
        jsonResponse(['error' => 'Database connection error'], 500);
        exit;
    }

    // Sanitize inputs
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $moduleId = mysqli_real_escape_string($conn, $moduleId);
    $levelId = mysqli_real_escape_string($conn, $levelId);
    $semester = mysqli_real_escape_string($conn, $semester);
    $session = mysqli_real_escape_string($conn, $session);
    $filename = mysqli_real_escape_string($conn, $filename);

    // Check if the imported_grades table exists, create it if not
    $checkTableQuery = "SHOW TABLES LIKE 'imported_grades'";
    $tableExists = mysqli_query($conn, $checkTableQuery);

    if (mysqli_num_rows($tableExists) == 0) {
        $createTableQuery = "CREATE TABLE imported_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            id_filiere INT NOT NULL,
            id_enseignant INT NOT NULL,
            id_module INT NOT NULL,
            id_niveau INT NOT NULL,
            semestre VARCHAR(50) NOT NULL,
            session VARCHAR(50) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (id_filiere),
            INDEX (id_enseignant),
            INDEX (id_module)
        )";

        if (!mysqli_query($conn, $createTableQuery)) {
            $error = mysqli_error($conn);
            error_log("Error creating imported_grades table: " . $error);
            mysqli_close($conn);
            jsonResponse(['error' => 'Error creating database table: ' . $error], 500);
            exit;
        }
    }

    // Insert the record
    $insertQuery = "INSERT INTO imported_grades (id_filiere, id_enseignant, id_module, id_niveau, semestre, session, filename)
                    VALUES ('$filiereId', '$teacherId', '$moduleId', '$levelId', '$semester', '$session', '$filename')";

    if (!mysqli_query($conn, $insertQuery)) {
        $error = mysqli_error($conn);
        error_log("Error inserting imported grade record: " . $error);
        mysqli_close($conn);
        jsonResponse(['error' => 'Error saving record: ' . $error], 500);
        exit;
    }

    mysqli_close($conn);
    jsonResponse(['success' => true, 'message' => 'File imported successfully', 'filename' => $filename], 200);
}

// Handle API requests
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getTeachers':
            getTeachersByCoordinatorFieldAPI();
            break;

        case 'getLevels':
            if (!isset($_GET['teacherId'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
                exit;
            }
            getLevelsByTeacherAndFieldAPI($_GET['teacherId']);
            break;

        case 'getSemesters':
            if (!isset($_GET['levelId'])) {
                jsonResponse(['error' => 'Level ID is required'], 400);
                exit;
            }
            getSemestersByLevelAPI($_GET['levelId']);
            break;

        case 'getModules':
            if (!isset($_GET['teacherId']) || !isset($_GET['levelId']) || !isset($_GET['semester'])) {
                jsonResponse(['error' => 'Teacher ID, Level ID, and Semester are required'], 400);
                exit;
            }
            getModulesByFiltersAPI($_GET['teacherId'], $_GET['levelId'], $_GET['semester']);
            break;

        case 'importGrades':
            importGradesFileAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            exit;
    }
}
?>
