<?php
require_once "../model/affectationModel.php";
// Nous n'avons plus besoin d'inclure uePreferencesModel.php car toutes les fonctions nécessaires sont dans affectationModel.php
require_once "../utils/response.php";

/**
 * API endpoint to get UE preferences for a department
 *
 * @param int $departmentId The department ID
 */
function getUePreferencesByDepartmentAPI($departmentId) {
    // Validate department ID
    if (!is_numeric($departmentId)) {
        jsonResponse(['error' => 'Invalid department ID'], 400);
    }

    $preferences = getUePreferencesByDepartment($departmentId);

    if (isset($preferences['error'])) {
        jsonResponse(['error' => $preferences['error']], 500);
    }

    jsonResponse(['data' => $preferences], 200);
}

/**
 * API endpoint to accept a UE preference
 */
function acceptUePreferenceAPI() {
    // Get request body
    $requestBody = file_get_contents('php://input');
    error_log("Request body: " . $requestBody);

    $data = json_decode($requestBody, true);
    error_log("Decoded data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['preference_id']) || !isset($data['academic_year']) ||
        $data['preference_id'] === '' || $data['academic_year'] === '') {
        error_log("Missing or empty required fields. preference_id: " . (isset($data['preference_id']) ? $data['preference_id'] : 'missing') .
                  ", academic_year: " . (isset($data['academic_year']) ? $data['academic_year'] : 'missing'));
        jsonResponse(['error' => 'Preference ID and academic year are required'], 400);
    }

    // Convert preference_id to integer if it's a string
    if (is_string($data['preference_id'])) {
        $data['preference_id'] = intval($data['preference_id']);
        error_log("Converted preference_id from string to integer: " . $data['preference_id']);
    }

    // Get optional comment
    $commentaire = isset($data['commentaire']) ? $data['commentaire'] : null;

    // Get department ID from session for authorization
    session_start();
    $departmentId = $_SESSION['user']['department_id'] ?? null;

    error_log("Using preference_id: " . $data['preference_id'] . ", academic_year: " . $data['academic_year'] .
              ", commentaire: " . ($commentaire ?? 'null') . ", department_id: " . ($departmentId ?? 'null'));

    if (!$departmentId) {
        error_log("Department ID not found in session");
        jsonResponse(['error' => 'Department ID not found in session. Please log in again.'], 401);
    }

    $result = acceptUePreference($data['preference_id'], $data['academic_year'], $commentaire, $departmentId);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Preference accepted successfully'], 200);
}

/**
 * API endpoint to reject a UE preference
 */
function rejectUePreferenceAPI() {
    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['preference_id'])) {
        jsonResponse(['error' => 'Preference ID is required'], 400);
    }

    // Get optional comment
    $commentaire = isset($data['commentaire']) ? $data['commentaire'] : null;

    // Get department ID from session for authorization
    session_start();
    $departmentId = $_SESSION['user']['department_id'] ?? null;

    error_log("Rejecting preference_id: " . $data['preference_id'] .
              ", commentaire: " . ($commentaire ?? 'null') . ", department_id: " . ($departmentId ?? 'null'));

    if (!$departmentId) {
        error_log("Department ID not found in session");
        jsonResponse(['error' => 'Department ID not found in session. Please log in again.'], 401);
    }

    $result = rejectUePreference($data['preference_id'], $commentaire, $departmentId);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Preference rejected successfully'], 200);
}

/**
 * API endpoint to get affectations for a department
 *
 * @param int $departmentId The department ID
 */
function getAffectationsByDepartmentAPI($departmentId) {
    // Validate department ID
    if (!is_numeric($departmentId)) {
        jsonResponse(['error' => 'Invalid department ID'], 400);
    }

    $affectations = getAffectationsByDepartment($departmentId);

    if (isset($affectations['error'])) {
        jsonResponse(['error' => $affectations['error']], 500);
    }

    jsonResponse(['data' => $affectations], 200);
}

/**
 * API endpoint to create a manual affectation
 */
function createManualAffectationAPI() {
    // Get request body
    $requestBody = file_get_contents('php://input');
    error_log("Request body: " . $requestBody);

    $data = json_decode($requestBody, true);
    error_log("Decoded data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['ue_id']) || !isset($data['teacher_id']) || !isset($data['academic_year']) ||
        $data['ue_id'] === '' || $data['teacher_id'] === '' || $data['academic_year'] === '') {
        error_log("Missing or empty required fields. ue_id: " . (isset($data['ue_id']) ? $data['ue_id'] : 'missing') .
                  ", teacher_id: " . (isset($data['teacher_id']) ? $data['teacher_id'] : 'missing') .
                  ", academic_year: " . (isset($data['academic_year']) ? $data['academic_year'] : 'missing'));
        jsonResponse(['error' => 'UE ID, teacher ID, and academic year are required'], 400);
    }

    // Get optional comment
    $commentaire = isset($data['commentaire']) ? $data['commentaire'] : null;

    // Get department ID from session for authorization
    session_start();
    $departmentId = $_SESSION['user']['department_id'] ?? null;

    error_log("Using ue_id: " . $data['ue_id'] . ", teacher_id: " . $data['teacher_id'] .
              ", academic_year: " . $data['academic_year'] .
              ", commentaire: " . ($commentaire ?? 'null') . ", department_id: " . ($departmentId ?? 'null'));

    if (!$departmentId) {
        error_log("Department ID not found in session");
        jsonResponse(['error' => 'Department ID not found in session. Please log in again.'], 401);
    }

    $result = createManualAffectation($data['teacher_id'], $data['ue_id'], $data['academic_year'], $commentaire);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
    }

    jsonResponse(['success' => true, 'message' => 'Affectation created successfully'], 200);
}

/**
 * API endpoint to get modules by department
 *
 * @param int $departmentId The department ID
 */
function getModulesByDepartmentAPI($departmentId) {
    // Validate department ID
    if (!is_numeric($departmentId)) {
        jsonResponse(['error' => 'Invalid department ID'], 400);
    }

    $modules = getModulesByDepartment($departmentId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 500);
    }

    jsonResponse(['data' => $modules], 200);
}

// Handle API requests based on action parameter
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getUePreferencesByDepartment':
            if (!isset($_GET['department_id'])) {
                jsonResponse(['error' => 'Department ID is required'], 400);
            }
            getUePreferencesByDepartmentAPI($_GET['department_id']);
            break;

        case 'acceptUePreference':
            acceptUePreferenceAPI();
            break;

        case 'rejectUePreference':
            rejectUePreferenceAPI();
            break;

        case 'getAffectationsByDepartment':
            if (!isset($_GET['department_id'])) {
                jsonResponse(['error' => 'Department ID is required'], 400);
            }
            getAffectationsByDepartmentAPI($_GET['department_id']);
            break;

        case 'createManualAffectation':
            createManualAffectationAPI();
            break;

        case 'getModulesByDepartment':
            if (!isset($_GET['department_id'])) {
                jsonResponse(['error' => 'Department ID is required'], 400);
            }
            getModulesByDepartmentAPI($_GET['department_id']);
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Action parameter is required'], 400);
}
?>
