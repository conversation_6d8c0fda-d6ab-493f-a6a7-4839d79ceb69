<?php
// Use a more robust path resolution to find the required files
$modelPath = __DIR__ . "/../model/noteModel.php";
$utilsPath = __DIR__ . "/../utils/response.php";

// Try different paths for model
if (file_exists($modelPath)) {
    require_once $modelPath;
} else {
    $altModelPath = __DIR__ . "/../../model/noteModel.php";
    if (file_exists($altModelPath)) {
        require_once $altModelPath;
    } else {
        die("Cannot find the noteModel.php file.");
    }
}

// Try different paths for utils
if (file_exists($utilsPath)) {
    require_once $utilsPath;
} else {
    $altUtilsPath = __DIR__ . "/../../utils/response.php";
    if (file_exists($altUtilsPath)) {
        require_once $altUtilsPath;
    } else {
        die("Cannot find the response.php file.");
    }
}

/**
 * API to get all notes
 */
function getAllNotesAPI() {
    $notes = getAllNotes();

    if (isset($notes['error'])) {
        jsonResponse(['error' => $notes['error'], 'success' => false], 404);
    }

    jsonResponse(['data' => $notes, 'success' => true], 200);
}

/**
 * API to get students by filters
 *
 * @param int|null $filiereId The field ID (optional)
 * @param int|null $niveauId The level ID (optional)
 * @param string|null $semestre The semester (optional)
 * @param string|null $session The session (optional)
 * @param int $moduleId The module ID
 */
function getStudentsByFiltersAPI($filiereId = null, $niveauId = null, $semestre = null, $session = null, $moduleId) {
    $students = getStudentsByFilters($filiereId, $niveauId, $semestre, $session, $moduleId);

    if (isset($students['error'])) {
        jsonResponse(['error' => $students['error'], 'success' => false], 404);
    }

    jsonResponse(['data' => $students, 'success' => true], 200);
}

/**
 * API to save grades
 *
 * @param array $grades Array of grade data
 */
function saveGradesAPI($grades) {
    $result = saveGrades($grades);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
    }

    jsonResponse(['message' => 'Grades saved successfully', 'success' => true], 200);
}

/**
 * API to create a new note
 *
 * @param array $data Note data
 */
function createNoteAPI($data) {
    // Validate required fields
    $requiredFields = ['id_etudiant', 'id_module', 'id_niveau', 'semestre', 'session', 'valeur'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || (is_string($data[$field]) && empty($data[$field]))) {
            jsonResponse(['error' => "Field '$field' is required", 'success' => false], 400);
            return;
        }
    }

    $result = createNote($data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }

    jsonResponse(['message' => 'Note created successfully', 'id' => $result['id'], 'success' => true], 201);
}

/**
 * API to send grades PDF to coordinator
 *
 * @param array $data Data containing PDF content and coordinator information
 */
function sendGradesToCoordinatorAPI($data) {
    // Validate required fields
    $requiredFields = ['pdf_content', 'module_id', 'filiere_id', 'niveau_id', 'semestre_id', 'session', 'module_name', 'filiere_name', 'teacher_name'];

    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || (is_string($data[$field]) && empty($data[$field]))) {
            jsonResponse(['error' => "Field '$field' is required", 'success' => false], 400);
            return;
        }
    }

    // Get the PDF content and metadata from the request
    $pdfContent = $data['pdf_content'];
    $moduleId = $data['module_id'];
    $filiereId = $data['filiere_id'];
    $niveauId = $data['niveau_id'];
    $semestreId = $data['semestre_id'];
    $session = $data['session'];
    $moduleName = $data['module_name'];
    $filiereName = $data['filiere_name'];
    $teacherName = $data['teacher_name'];

    // Priorité 1: Utiliser l'ID de l'enseignant envoyé par le frontend
    $teacherId = isset($data['teacher_id']) ? $data['teacher_id'] : null;

    // Priorité 2: Utiliser l'ID de l'enseignant de la session si disponible
    if (!$teacherId) {
        $teacherId = isset($_SESSION['user']['teacher_id']) ? $_SESSION['user']['teacher_id'] : null;
    }

    // Priorité 3: Utiliser une valeur par défaut
    if (!$teacherId) {
        error_log("Teacher ID not found in request or session, using default value");
        $teacherId = 1; // Default value if teacher ID is not available
    }

    // Log pour débogage
    error_log("Using teacher ID: " . $teacherId . " for PDF record");

    // Generate a unique filename
    $timestamp = date('YmdHis');
    $filename = "notes_" . $moduleId . "_" . $timestamp . ".pdf";

    // Define the uploads directory path
    $uploadsDir = __DIR__ . "/../uploads";
    $pdfsDir = $uploadsDir . "/pdfs";
    $filePath = $pdfsDir . "/" . $filename;

    // Create uploads directory if it doesn't exist
    if (!file_exists($uploadsDir)) {
        error_log("Creating uploads directory: " . $uploadsDir);
        if (!mkdir($uploadsDir, 0777, true)) {
            error_log("Failed to create uploads directory: " . $uploadsDir);
        }
    }

    // Create pdfs directory if it doesn't exist
    if (!file_exists($pdfsDir)) {
        error_log("Creating pdfs directory: " . $pdfsDir);
        if (!mkdir($pdfsDir, 0777, true)) {
            error_log("Failed to create pdfs directory: " . $pdfsDir);
        }
    }

    // Save the PDF file
    $pdfData = base64_decode(preg_replace('#^data:application/pdf;base64,#', '', $pdfContent));
    file_put_contents($filePath, $pdfData);

    // Include the pdf_grades model
    require_once __DIR__ . "/../model/pdfGradesModel.php";

    // Save the PDF record to the pdf_grades table
    $result = savePdfGrade($filiereId, $teacherId, $moduleId, $niveauId, $semestreId, $session, $filename);

    if (isset($result['error'])) {
        error_log("Error saving PDF record: " . $result['error']);
        jsonResponse([
            'error' => 'Erreur lors de l\'enregistrement du PDF: ' . $result['error'],
            'success' => false
        ], 500);
        return;
    }

    // Create a notification for the coordinator
    require_once __DIR__ . "/../model/notificationsModel.php";

    $title = "Nouvelles notes pour le module $moduleName";
    $message = "L'enseignant $teacherName a envoyé les notes des étudiants pour le module $moduleName de la filière $filiereName.";
    $type = "grades";

    // Create the notification
    $notificationResult = createNotification($title, $message, null, $filename, $type);

    if ($notificationResult) {
        jsonResponse([
            'message' => 'Notes envoyées au coordinateur avec succès',
            'success' => true,
            'file_path' => $filename
        ], 200);
    } else {
        // Even if notification fails, the PDF was saved successfully
        jsonResponse([
            'message' => 'Notes envoyées au coordinateur avec succès (notification non créée)',
            'success' => true,
            'file_path' => $filename
        ], 200);
    }
}

/**
 * API to update a note
 *
 * @param int $id Note ID
 * @param array $data Note data
 */
function updateNoteAPI($id, $data) {
    $result = updateNote($id, $data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }

    jsonResponse(['message' => 'Note updated successfully', 'success' => true], 200);
}

/**
 * API to update a student's grade
 *
 * @param int $studentId Student ID
 * @param int $moduleId Module ID
 * @param int $niveauId Level ID
 * @param int $filiereId Field ID
 * @param string $semestre Semester
 * @param string $session Session
 * @param float $grade Grade value
 */
function updateStudentGradeAPI($studentId, $moduleId, $niveauId, $filiereId, $semestre, $session, $grade) {
    // Log les paramètres reçus
    error_log("updateStudentGradeAPI - Paramètres reçus: studentId=$studentId, moduleId=$moduleId, niveauId=$niveauId, filiereId=$filiereId, semestre=$semestre, session=$session, grade=$grade");

    // Vérifier que les paramètres sont valides
    if (empty($studentId) || empty($moduleId) || empty($session) || $grade === null) {
        $errorMsg = "Paramètres manquants ou invalides";
        error_log("updateStudentGradeAPI - Erreur: $errorMsg");
        jsonResponse(['error' => $errorMsg, 'success' => false], 400);
        return;
    }

    // Convertir le grade en nombre
    $grade = floatval($grade);

    // Appeler la fonction du modèle
    $result = updateStudentGrade($studentId, $moduleId, $niveauId, $filiereId, $semestre, $session, $grade);

    // Log le résultat
    error_log("updateStudentGradeAPI - Résultat: " . print_r($result, true));

    if (isset($result['error'])) {
        error_log("updateStudentGradeAPI - Erreur: " . $result['error']);
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }

    error_log("updateStudentGradeAPI - Succès: Note mise à jour pour l'étudiant $studentId");
    jsonResponse(['message' => 'Grade updated successfully', 'success' => true], 200);
}

/**
 * API to get top performing students by module
 *
 * @param int $moduleId Module ID
 * @param string $session Session
 * @param int $limit Number of top students to return
 */
function getTopNotesByModuleAPI($moduleId, $session, $limit = 3) {
    $topNotes = getTopNotesByModule($moduleId, $session, $limit);

    if (isset($topNotes['error'])) {
        jsonResponse(['error' => $topNotes['error'], 'success' => false], 404);
    }

    jsonResponse(['data' => $topNotes, 'success' => true], 200);
}

/**
 * API to delete a note
 *
 * @param int $id Note ID
 */
function deleteNoteAPI($id) {
    $result = deleteNote($id);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error'], 'success' => false], 500);
        return;
    }

    jsonResponse(['message' => 'Note deleted successfully', 'success' => true], 200);
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'get_student_grades':
            $filiereId = isset($_GET['filiere']) ? $_GET['filiere'] : null;
            $niveauId = isset($_GET['niveau']) ? $_GET['niveau'] : null;
            $semestre = isset($_GET['semestre']) ? $_GET['semestre'] : null;
            $session = isset($_GET['session']) ? $_GET['session'] : null;
            $moduleId = isset($_GET['module']) ? $_GET['module'] : null;

            getStudentsByFiltersAPI($filiereId, $niveauId, $semestre, $session, $moduleId);
            break;

        case 'get_top_performers':
            $moduleId = isset($_GET['module']) ? $_GET['module'] : null;
            $session = isset($_GET['session']) ? $_GET['session'] : null;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 3;

            getTopNotesByModuleAPI($moduleId, $session, $limit);
            break;

        default:
            jsonResponse(['error' => 'Action not recognized', 'success' => false], 400);
    }

    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    switch ($action) {
        case 'update_student_grade':
            $studentId = isset($_POST['student_id']) ? $_POST['student_id'] : null;
            $moduleId = isset($_POST['module']) ? $_POST['module'] : null;
            $niveauId = isset($_POST['niveau']) ? $_POST['niveau'] : null;
            $filiereId = isset($_POST['filiere']) ? $_POST['filiere'] : null;
            $semestre = isset($_POST['semestre']) ? $_POST['semestre'] : null;
            $session = isset($_POST['session']) ? $_POST['session'] : null;
            $grade = isset($_POST['note']) ? $_POST['note'] : null;

            if (!$studentId || !$moduleId || !$session || $grade === null) {
                jsonResponse(['error' => 'Missing required parameters', 'success' => false], 400);
                exit;
            }

            updateStudentGradeAPI($studentId, $moduleId, $niveauId, $filiereId, $semestre, $session, $grade);
            break;

        case 'save_grades':
            if (!isset($_POST['grades']) || !is_array($_POST['grades'])) {
                jsonResponse(['error' => 'Grades data is required', 'success' => false], 400);
                exit;
            }

            saveGradesAPI($_POST['grades']);
            break;

        default:
            jsonResponse(['error' => 'Action not recognized', 'success' => false], 400);
    }

    exit;
}
?>