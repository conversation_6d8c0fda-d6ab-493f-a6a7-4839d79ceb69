/* Upload Grades Page Styles */

/* Main container styles */
.container-fluid {
    padding-top: 20px;
}

/* Card styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    border-bottom: none;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Filter card styles */
.filter-card {
    background: linear-gradient(to right bottom, #ffffff, #f8f9fa);
}

.filter-icon i {
    font-size: 1.2rem;
    color: #6c63ff;
}

.filter-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.filter-select {
    border-radius: 10px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    width: 100%;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: #6c63ff;
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
}

.filter-group {
    margin-bottom: 15px;
}

/* Button styles */
.button-container {
    margin-top: 15px;
}

.btn-primary {
    background: linear-gradient(to right, #6c63ff, #4e42f5);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(to right, #5a52d5, #3f35c8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 99, 255, 0.3);
}

.btn-success {
    background: linear-gradient(to right, #28a745, #20c997);
    border: none;
    border-radius: 10px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(to right, #218838, #1aa179);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Applied filters styles */
.applied-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-badge {
    background: linear-gradient(to right, #e9ecef, #dee2e6);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    color: #495057;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.filter-badge-label {
    font-weight: 600;
}

/* Table styles */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-top: none;
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(108, 99, 255, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(108, 99, 255, 0.1);
}

/* Grade input styles */
.grade-input {
    width: 80px;
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    text-align: center;
    transition: all 0.3s ease;
}

.grade-input:focus {
    border-color: #6c63ff;
    box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
    outline: none;
}

/* Animation styles */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Alert styles */
.alert {
    border-radius: 10px;
    padding: 15px 20px;
}

.alert-info {
    background-color: #e1f5fe;
    border-color: #b3e5fc;
    color: #0288d1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-group {
        margin-bottom: 15px;
    }
    
    .card-header {
        padding: 12px 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table th, .table td {
        padding: 10px;
    }
}