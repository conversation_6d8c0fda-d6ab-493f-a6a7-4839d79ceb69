<?php
require_once __DIR__ . '/../config/db.php';

function getNotifications() {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("
            SELECT id, title, message, media_url, file_path, type, is_read, created_at
            FROM notifications
            ORDER BY created_at DESC
        ");

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . $conn->error);
            return [];
        }

        $result = $stmt->execute();

        if (!$result) {
            error_log("Erreur d'exécution de la requête: " . $stmt->error);
            return [];
        }

        $result = $stmt->get_result();
        $notifications = [];

        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }

        return $notifications;
    } catch (Exception $e) {
        error_log("Exception dans getNotifications: " . $e->getMessage());
        return [];
    }
}

function markNotificationAsRead($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE notifications
        SET is_read = 1
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function deleteNotification($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        DELETE FROM notifications
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function markAllNotificationsAsRead() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE notifications
        SET is_read = 1
    ");
    return $stmt->execute();
}

function createNotification($title, $message, $media_url = null, $file_path = null, $type = 'message') {
    $conn = getConnection();
    $stmt = $conn->prepare("
        INSERT INTO notifications (title, message, media_url, file_path, type)
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->bind_param("sssss", $title, $message, $media_url, $file_path, $type);
    return $stmt->execute();
}

function getUnreadNotificationsCount() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM notifications
        WHERE is_read = 0
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    return $row['count'];
}
?>