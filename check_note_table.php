<?php
// Connexion à la base de données
$host = "localhost";
$username = "root";
$password = "";
$database = "school_management";

$conn = mysqli_connect($host, $username, $password, $database);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Vérifier la structure de la table note
$sql = "DESCRIBE note";
$result = mysqli_query($conn, $sql);

if (!$result) {
    die("Error: " . mysqli_error($conn));
}

echo "<h2>Structure de la table note</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}

echo "</table>";

// Vérifier si la table note contient des données
$sql = "SELECT COUNT(*) as count FROM note";
$result = mysqli_query($conn, $sql);

if (!$result) {
    die("Error: " . mysqli_error($conn));
}

$row = mysqli_fetch_assoc($result);
echo "<p>Nombre de notes dans la table: " . $row['count'] . "</p>";

// Vérifier les noms des champs dans la table note
$sql = "SELECT * FROM note LIMIT 1";
$result = mysqli_query($conn, $sql);

if (!$result) {
    die("Error: " . mysqli_error($conn));
}

if (mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);
    echo "<h3>Noms des champs dans la table note</h3>";
    echo "<ul>";
    foreach ($row as $field => $value) {
        echo "<li>" . $field . "</li>";
    }
    echo "</ul>";
}

mysqli_close($conn);
?>
